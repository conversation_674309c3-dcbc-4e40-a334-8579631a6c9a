syntax = "proto3";

package addr.v1;
option go_package = "halalplus/app/user-account-svc/api/addr/v1;v1";
import "google/protobuf/wrappers.proto";
import "common/base.proto"; // 假设 common.Error 定义在这


// 用户地址服务
// HTTP /api/user-account/addr/v1/AddressService
service AddressService {
  // 根据经纬度查询当前所在地址信息（逆地理编码）
  // GET /api/user-account/addr/v1/AddressService/GetAddressByLocation
  rpc GetAddressByLocation (GetAddressByLocationReq) returns (GetAddressByLocationRes);

  // 查询某一行政区域下的子区域列表
  // 用于四级联动场景，比如传入省份编码，返回该省的所有市
  // 若 parent_code 为空，则返回顶级（省级）区域列表
  // GET /api/user-account/addr/v1/AddressService/GetSubRegions
  rpc GetSubRegions (GetSubRegionsReq) returns (GetSubRegionsRes);

  // 根据获取行政地址的经纬度坐标
  // GET /api/user-account/addr/v1/AddressService/GetLatlng
  rpc GetLatlng (GetLatlngReq) returns (GetLatlngRes);

  // 获取当前用户的收货地址列表
  // 可选择仅返回默认地址或全部地址
  // GET /api/user-account/addr/v1/AddressService/GetList
  rpc GetList (GetAddressListReq) returns (GetAddressListRes);

  // 新增或修改用户地址
  // 若请求中的 id 为空或为 0，则新增地址
  // 否则视为更新已有地址信息
  // POST /api/user-account/addr/v1/AddressService/Save
  rpc Save (SaveAddressReq) returns (SaveAddressRes);

  // 删除用户地址
  // 根据地址 ID 删除对应记录，删除后若是默认地址，前端需提示用户设置新的默认地址
  // POST /api/user-account/addr/v1/AddressService/Delete
  rpc Delete (DeleteAddressReq) returns (DeleteAddressRes);
}

message GetLatlngReq {
  string code = 1;
}

message GetLatlngResData {
  string code = 1;         // 区划代码,唯一
  string name = 2;         // 区划名称
  string parent_code = 3;  // 父级代码
  AddressLevel level = 4;         // 层级
  // 纬度，例如 23.118869
  double latitude = 5;
  // 经度，例如 113.370062
  double longitude = 6;
}

message GetLatlngRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  GetLatlngResData data = 4;
}

// 根据经纬度查询地址请求
message GetAddressByLocationReq {
  // 纬度，例如 23.118869
  double latitude = 1;

  // 经度，例如 113.370062
  double longitude = 2;
}

// 用户地址结构
message GetAddressByLocationResData {

  // 地址级联信息（省）
  string level1_code = 2;
  string level1_name = 3;

  // 地址级联信息（市）
  string level2_code = 4;
  string level2_name = 5;

  // 地址级联信息（区/镇）
  string level3_code = 6;
  string level3_name = 7;

  // 地址级联信息（村/社区）
  string level4_code = 8;
  string level4_name = 9;

  // 详细地址（门牌号）
  string address = 10;

  repeated AddressItem list = 11;
}
message GetAddressByLocationRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  GetAddressByLocationResData data = 4;
}

message GetSubRegionsReq {
  string parent_code = 1;  // 父级编码，空则返回顶级区域
}

enum AddressLevel {
  LEVEL_UNSPECIFIED = 0; // 默认值（建议保留）
  PROVINCE = 1;           // 省
  CITY = 2;               // 市
  DISTRICT = 3;           // 区/镇
  VILLAGE = 4;            // 村/社区
}

// === 印尼地址 ===
// 数据来源 github.com/erlange/Kodepos-Wilayah-Indonesia
message AddressItem {
  string code = 1;         // 区划代码,唯一
  string name = 2;         // 区划名称
  string parent_code = 3;  // 父级代码
  AddressLevel level = 4;         // 层级
  string zip_code = 5;      // 邮编（可为空）
}

message GetSubRegionsResData {
  repeated AddressItem list = 1;
}

message GetSubRegionsRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  GetSubRegionsResData data = 4;
}

// 用户地址结构
message UserAddress {
  // 地址ID（主键）
  uint64 id = 1;

  // 地址级联信息（省）
  string level1_code = 2;
  string level1_name = 3;

  // 地址级联信息（市）
  string level2_code = 4;
  string level2_name = 5;

  // 地址级联信息（区/镇）
  string level3_code = 6;
  string level3_name = 7;

  // 地址级联信息（村/社区）
  string level4_code = 8;
  string level4_name = 9;

  // 详细地址（门牌号）
  string address = 10;

  // 电话的国家区号
  string area_code = 11;

  // 手机号码
  string phone_num = 12;

  // 收货人姓名
  string receiver = 13;

  // 是否默认地址
  bool is_default = 14;

  int64 create_time = 15;
  int64 update_time = 16;
}
// 获取地址列表请求
message GetAddressListReq {
  // 不设置时标识获取所有
  // 是否筛选默认地址（不传表示全部）
  google.protobuf.BoolValue is_default = 1;
}

message GetAddressListResData {
  repeated UserAddress list = 1;
}

// 地址列表返回
message GetAddressListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  GetAddressListResData data = 4;
}

// 添加或更新地址的请求
message SaveAddressReq {
  // 地址ID（主键），没有id则是新增
  uint64 id = 1;

  // 地址级联信息（省）
  string level1_code = 2;
  string level1_name = 3;

  // 地址级联信息（市）
  string level2_code = 4;
  string level2_name = 5;

  // 地址级联信息（区/镇）
  string level3_code = 6;
  string level3_name = 7;

  // 地址级联信息（村/社区）
  string level4_code = 8;
  string level4_name = 9;

  // 详细地址（门牌号）
  string address = 10;

  // 电话的国家区号
  string area_code = 11;

  // 手机号码
  string phone_num = 12;

  // 收货人姓名
  string receiver = 13;

  // 是否默认地址
  google.protobuf.BoolValue is_default = 14;
}

message SaveAddressRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message DeleteAddressReq {
  uint64 id = 1;
}

// 删除地址
message DeleteAddressRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}
