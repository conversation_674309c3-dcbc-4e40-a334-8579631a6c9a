"accept-language"="english"
"user.account.required" = "Account cannot be empty"
"user.account.length" = "Account length should be between {min} and {max} characters"
"user.account.occupied" = "Account %s is already taken"
"user.accountOrPassword.invalid" = "Invalid account or password"
"user.phoneNum.ontExist" = "Phone number is not registered"
"user.token.invalid" = "Invalid token"
"user.id.notExist" = "Account ID %d does not exist"
"user.wallet.notExist" = "Account %s wallet does not exist"
"user.country.id.notExist" = "Country ID %d does not exist"
"user.phoneNum.occupied" = "Phone number %s is already taken"
"user.email.occupied" = "Email %s is already taken"
"user.phoneNumBind.error" = "Error binding phone number"
"user.bankCard.occupied" = "Bank card %s is already bound"
"user.coinAddress.occupied" = "Wallet address %s is already bound"
"user.alipay.occupied" = "Alipay account %s is already bound"
"user.bankCard.add.error" = "Error adding bank card"
"user.oddsType.update.error" = "Error updating odds type"
"user.supplierUser.account.occupied" = "Account is already taken"

"user.account.forbid.underAge18" = "Registration is forbidden for users under 18"
"user.account.phoneNum.existed" = "Phone number is already registered"
"user.account.email.existed" = "Email is already registered"
"user.account.account.existed" = "Username is already registered"
"user.account.phoneNum.captcha.mismatch" = "Phone verification code does not match"
"user.account.email.captcha.mismatch" = "Email verification code does not match"
"user.account.banned" = "Account is banned"
"account.phone.unregistered" = "Phone number is not registered"
"account.email.unregistered" = "Email is not registered"
"user.account.password.error" = "Incorrect account or password"
"account.password.reset.errorToken" = "Incorrect token for password reset request"
"account.password.reset.unconfirmed" = "Email or phone not authorized for password change"
"account.frequency.exceeds.limit" = "SMS send frequency exceeds limit!"
"account.count.exceeds.limit" = "Today’s send limit reached, please try again in 24 hours"

"account.opt.code.expired.incorrect" = "Verification code has expired or is incorrect"
"account.resetPwd.expired.incorrect" = "Password reset token has expired or is incorrect"
"user.account.reset.password.error" = "Error executing password reset in database"
"account.phone.entered.incorrectly" = "Phone number entered incorrectly!"
"user.nickname.changed.limit" = "Nickname can only be changed once within 180 days"

"account.auth.expired.incorrect" = "Authorization token verification failed!"
"user.phoneNum.is.binding" = "Phone number has been successfully bound"
"user.email.is.binding" = "Email has been successfully bound"

"user.edit.profile.sensitive.input" = "Input contains sensitive words"
"user.login.ban.time.second" = "Remaining login ban time: %d seconds"
"user.login.ban.time.minutes" = "Remaining login ban time: %d minutes"
"user.login.ban.time.hours" = "Remaining login ban time: %d hours"
"user.login.ban.time.day" = "Remaining login ban time: %d days"

"user.edit.profile.nickname.illegal" = "Nickname cannot contain underscores"

"user.risk.control.black" = "User information involves blacklist risk control"
"user.risk.withdrawal.prohibited" = "User is prohibited from withdrawing"
"user.risk.recharge.prohibited" = "User is prohibited from recharge"


"account.phone.different" = "Verification phone does not match registered phone."
"account.email.different" = "Verification email does not match registered email."

"user.sign.up.ip.limit" = "Current IP registration count exceeds limit, please try again tomorrow"
"user.sign.in.ip.limit" = "Current IP login count exceeds limit, please try again tomorrow"
"user.pay.password.must.be.set" = "Payment password must be set"

"use.bankcard.max.limit.num" = "You can have a maximum of %d bank cards"
"use.bankcard.accountNum.duplicate" = "Duplicate bank card number"
"use.coinAddress.duplicate" = "Duplicate cryptocurrency address"

"use.coinAddress.max.limit.num" = "You can have a maximum of %d cryptocurrency addresses"

"account.access.token.expired.incorrect" = "Access token is invalid"

"user.payment.password.error" = "Incorrect payment password"

"user.frequent" = "Request is frequent, please try again later"

"use.account.phoneNum.must.be.set" = "Phone number must be bound"
"use.bankcard.id.not.find" = "Bank card not found"
"use.coinAddress.id.not.find" = "Cryptocurrency address not found"

"user.not.found" = "User not found"

"use.wallet.main.balance.not.enough" = "Insufficient balance"

"user.in.blacklist" = "Registration unsuccessful. Please contact customer service."
"user.unknown.error" = "unknown error"
"user.invalid.parameter" = "invalid parameter"