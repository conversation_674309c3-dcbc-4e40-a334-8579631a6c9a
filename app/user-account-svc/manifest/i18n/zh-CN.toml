"accept-language"="中文"
"user.account.required" = "账号不能为空"
"user.account.length" = "账号长度应在 {min} 到 {max} 个字符之间"
"user.account.occupied" = "账号 %s 已被占用"
"user.accountOrPassword.invalid" = "账号或密码无效"
"user.phoneNum.ontExist" = "手机号未注册"
"user.token.invalid" = "无效的令牌"
"user.id.notExist" = "账号 ID %d 不存在"
"user.wallet.notExist" = "账号 %s 的钱包不存在"
"user.country.id.notExist" = "国家 ID %d 不存在"
"user.phoneNum.occupied" = "手机号 %s 已被占用"
"user.email.occupied" = "邮箱 %s 已被占用"
"user.phoneNumBind.error" = "绑定手机号出错"
"user.bankCard.occupied" = "银行卡 %s 已被绑定"
"user.coinAddress.occupied" = "钱包地址 %s 已被绑定"
"user.alipay.occupied" = "支付宝账号 %s 已被绑定"
"user.bankCard.add.error" = "添加银行卡出错"
"user.oddsType.update.error" = "更新赔率类型出错"
"user.supplierUser.account.occupied" = "账号已被占用"

"user.account.forbid.underAge18" = "未满 18 岁禁止注册"
"user.account.phoneNum.existed" = "手机号已注册"
"user.account.email.existed" = "邮箱已注册"
"user.account.account.existed" = "用户名已注册"
"user.account.phoneNum.captcha.mismatch" = "手机验证码不匹配"
"user.account.email.captcha.mismatch" = "邮箱验证码不匹配"
"user.account.banned" = "账号已被封禁"
"account.phone.unregistered" = "手机号未注册"
"account.email.unregistered" = "邮箱未注册"
"user.account.password.error" = "账号或密码错误"
"account.password.reset.errorToken" = "密码重置请求的令牌不正确"
"account.password.reset.unconfirmed" = "邮箱或手机号未授权修改密码"
"account.frequency.exceeds.limit" = "短信发送频率超出限制！"
"account.count.exceeds.limit" = "今日发送次数已达上限，请 24 小时后再试"

"account.opt.code.expired.incorrect" = "验证码已过期或不正确"
"account.resetPwd.expired.incorrect" = "密码重置令牌已过期或不正确"
"user.account.reset.password.error" = "执行数据库密码重置出错"
"account.phone.entered.incorrectly" = "手机号输入错误！"
"user.nickname.changed.limit" = "昵称 180 天内只能修改一次"

"account.auth.expired.incorrect" = "授权令牌验证失败！"
"user.phoneNum.is.binding" = "手机号绑定成功"
"user.email.is.binding" = "邮箱绑定成功"

"user.edit.profile.sensitive.input" = "输入内容包含敏感词"
"user.login.ban.time.second" = "剩余登录封禁时间：%d 秒"
"user.login.ban.time.minutes" = "剩余登录封禁时间：%d 分钟"
"user.login.ban.time.hours" = "剩余登录封禁时间：%d 小时"
"user.login.ban.time.day" = "剩余登录封禁时间：%d 天"

"user.edit.profile.nickname.illegal" = "昵称不能包含下划线"

"user.risk.control.black" = "用户信息涉及风控黑名单"
"user.risk.withdrawal.prohibited" = "用户禁止提现"
"user.risk.recharge.prohibited" = "用户禁止充值"

"account.phone.different" = "验证手机号与注册手机号不一致"
"account.email.different" = "验证邮箱与注册邮箱不一致"

"user.sign.up.ip.limit" = "当前 IP 注册数量超出限制，请明天再试"
"user.sign.in.ip.limit" = "当前 IP 登录次数超出限制，请明天再试"
"user.pay.password.must.be.set" = "必须设置支付密码"

"use.bankcard.max.limit.num" = "最多只能绑定 %d 张银行卡"
"use.bankcard.accountNum.duplicate" = "银行卡号重复"
"use.coinAddress.duplicate" = "加密货币地址重复"

"use.coinAddress.max.limit.num" = "最多只能绑定 %d 个加密货币地址"

"account.access.token.expired.incorrect" = "访问令牌无效"

"user.payment.password.error" = "支付密码错误"

"user.frequent" = "请求过于频繁，请稍后再试"

"use.account.phoneNum.must.be.set" = "必须绑定手机号"
"use.bankcard.id.not.find" = "未找到银行卡"
"use.coinAddress.id.not.find" = "未找到加密货币地址"

"user.not.found" = "用户不存在"

"use.wallet.main.balance.not.enough" = "余额不足"

"user.in.blacklist" = "注册失败，请联系客服"

"user.unknown.error" = "未知错误"
"user.invalid.parameter" = "参数不合法"