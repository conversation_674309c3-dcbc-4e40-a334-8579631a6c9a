"accept-language"="indonesia"
"user.account.required" = "Akun tidak boleh kosong"
"user.account.length" = "Panjang akun harus antara {min} dan {max} karakter"
"user.account.occupied" = "Akun %s sudah digunakan"
"user.accountOrPassword.invalid" = "Akun atau kata sandi tidak valid"
"user.phoneNum.ontExist" = "Nomor telepon belum terdaftar"
"user.token.invalid" = "Token tidak valid"
"user.id.notExist" = "ID akun %d tidak ada"
"user.wallet.notExist" = "Dompet akun %s tidak ada"
"user.country.id.notExist" = "ID negara %d tidak ada"
"user.phoneNum.occupied" = "Nomor telepon %s sudah digunakan"
"user.email.occupied" = "Email %s sudah digunakan"
"user.phoneNumBind.error" = "Kesalahan saat mengikat nomor telepon"
"user.bankCard.occupied" = "Kartu bank %s sudah terikat"
"user.coinAddress.occupied" = "Alamat dompet %s sudah terikat"
"user.alipay.occupied" = "Akun Alipay %s sudah terikat"
"user.bankCard.add.error" = "Kesalahan menambahkan kartu bank"
"user.oddsType.update.error" = "Kesalahan memperbarui jenis odds"
"user.supplierUser.account.occupied" = "Akun sudah digunakan"

"user.account.forbid.underAge18" = "Pendaftaran dilarang untuk pengguna di bawah 18 tahun"
"user.account.phoneNum.existed" = "Nomor telepon sudah terdaftar"
"user.account.email.existed" = "Email sudah terdaftar"
"user.account.account.existed" = "Nama pengguna sudah terdaftar"
"user.account.phoneNum.captcha.mismatch" = "Kode verifikasi telepon tidak cocok"
"user.account.email.captcha.mismatch" = "Kode verifikasi email tidak cocok"
"user.account.banned" = "Akun diblokir"
"account.phone.unregistered" = "Nomor telepon belum terdaftar"
"account.email.unregistered" = "Email belum terdaftar"
"user.account.password.error" = "Akun atau kata sandi salah"
"account.password.reset.errorToken" = "Token tidak benar untuk permintaan reset kata sandi"
"account.password.reset.unconfirmed" = "Email atau telepon tidak diotorisasi untuk mengubah kata sandi"
"account.frequency.exceeds.limit" = "Frekuensi pengiriman SMS melebihi batas!"
"account.count.exceeds.limit" = "Batas pengiriman hari ini tercapai, silakan coba lagi dalam 24 jam"

"account.opt.code.expired.incorrect" = "Kode verifikasi telah kedaluwarsa atau salah"
"account.resetPwd.expired.incorrect" = "Token reset kata sandi telah kedaluwarsa atau salah"
"user.account.reset.password.error" = "Kesalahan menjalankan reset kata sandi di database"
"account.phone.entered.incorrectly" = "Nomor telepon dimasukkan dengan salah!"
"user.nickname.changed.limit" = "Nama panggilan hanya bisa diganti sekali dalam 180 hari"

"account.auth.expired.incorrect" = "Verifikasi token otorisasi gagal!"
"user.phoneNum.is.binding" = "Nomor telepon berhasil diikat"
"user.email.is.binding" = "Email berhasil diikat"

"user.edit.profile.sensitive.input" = "Input mengandung kata-kata sensitif"
"user.login.ban.time.second" = "Sisa waktu larangan login: %d detik"
"user.login.ban.time.minutes" = "Sisa waktu larangan login: %d menit"
"user.login.ban.time.hours" = "Sisa waktu larangan login: %d jam"
"user.login.ban.time.day" = "Sisa waktu larangan login: %d hari"

"user.edit.profile.nickname.illegal" = "Nama panggilan tidak boleh mengandung garis bawah"

"user.risk.control.black" = "Informasi pengguna terkait kontrol risiko daftar hitam"
"user.risk.withdrawal.prohibited" = "Pengguna dilarang melakukan penarikan"
"user.risk.recharge.prohibited" = "Pengguna dilarang melakukan isi ulang"

"account.phone.different" = "Nomor telepon verifikasi tidak cocok dengan nomor telepon terdaftar."
"account.email.different" = "Email verifikasi tidak cocok dengan email terdaftar."

"user.sign.up.ip.limit" = "Jumlah pendaftaran IP saat ini melebihi batas, silakan coba lagi besok"
"user.sign.in.ip.limit" = "Jumlah login IP saat ini melebihi batas, silakan coba lagi besok"
"user.pay.password.must.be.set" = "Kata sandi pembayaran harus diatur"

"use.bankcard.max.limit.num" = "Anda dapat memiliki maksimal %d kartu bank"
"use.bankcard.accountNum.duplicate" = "Nomor kartu bank duplikat"
"use.coinAddress.duplicate" = "Alamat cryptocurrency duplikat"

"use.coinAddress.max.limit.num" = "Anda dapat memiliki maksimal %d alamat cryptocurrency"

"account.access.token.expired.incorrect" = "Token akses tidak valid"

"user.payment.password.error" = "Kata sandi pembayaran salah"

"user.frequent" = "Permintaan terlalu sering, silakan coba lagi nanti"

"use.account.phoneNum.must.be.set" = "Nomor telepon harus diikat"
"use.bankcard.id.not.find" = "Kartu bank tidak ditemukan"
"use.coinAddress.id.not.find" = "Alamat cryptocurrency tidak ditemukan"

"user.not.found" = "Pengguna tidak ditemukan"

"use.wallet.main.balance.not.enough" = "Saldo tidak mencukupi"

"user.in.blacklist" = "Pendaftaran gagal. Silakan hubungi layanan pelanggan."
