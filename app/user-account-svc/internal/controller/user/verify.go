package user

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
)

func (c *Controller) SendVerifyCode(ctx context.Context, req *v1.SendVerifyCodeReq) (res *v1.SendVerifyCodeRes, err error) {
	g.Log().Line().Debug(ctx, req)
	var mp model.PhoneInfo
	if req.PhoneInfo != nil {
		mp = model.PhoneInfo{
			AreaCode: req.PhoneInfo.AreaCode,
			PhoneNum: req.PhoneInfo.PhoneNum,
		}
	}

	// 登录注册
	if req.VerifyCodeScene == v1.VerifyCodeScene_LOGIN || req.VerifyCodeScene == v1.VerifyCodeScene_SIGN_UP {

		if req.VerifyCodeChannel == v1.VerifyCodeChannel_SMS {
			// 发送登录短信验证码
			if !mp.IsValid() {
				g.Log().Debug(ctx, "VerifyCodeChannel_SMS invalid phone", mp)
				return nil, errno.T(ctx, errno.CodeUserInvalidParameter)
			}
			err = service.Verify().SendLoginCode(ctx, &mp)
			if err != nil {
				g.Log().Error(ctx, "发送登录短信验证码", err)
				return nil, errno.T(ctx, errno.CodeSendOtpError)
			}
		} else if req.VerifyCodeChannel == v1.VerifyCodeChannel_EMAIL {
			// 发送登录email验证码
			if !utility.IsValidEmail(req.Email) {
				g.Log().Debug(ctx, "VerifyCodeChannel_EMAIL invalid email", req.Email)
				return nil, errno.T(ctx, errno.CodeInvalidEmailError)
			}
			err = service.Verify().SendEmailLoginCode(ctx, req.Email)
			if err != nil {
				g.Log().Error(ctx, "发送登录email验证码", err)
				return nil, errno.T(ctx, errno.CodeSendEmailError)
			}
		} else {
			return nil, errno.T(ctx, errno.CodeUserInvalidParameter)
		}

		if err != nil {
			g.Log().Error(ctx, err)
			return nil, err
		}
	} else if req.VerifyCodeScene == v1.VerifyCodeScene_BIND_PHONE {
		// 更换手机号码
		if !mp.IsValid() {
			g.Log().Debug(ctx, "VerifyCodeScene_BIND_PHONE invalid phone", mp)
			return nil, errno.T(ctx, errno.CodeUserInvalidParameter)
		}
		// 手机号码必须不存在
		count, err := dao.User.Ctx(ctx).Where(dao.User.Columns().PhoneNum, mp.GetPhoneNum()).Count()
		if err != nil {
			g.Log().Error(ctx, err)
			return nil, errno.Te(ctx, errno.CodeUserPhoneNumberNotExisted, err)
		}
		if count > 0 {
			return nil, errno.T(ctx, errno.CodeUserPhoneNumberNotExisted)
		}
		err = service.Verify().SendBindPhoneCode(ctx, &mp)
		if err != nil {
			g.Log().Error(ctx, err)
			return nil, err
		}
	} else if req.VerifyCodeScene == v1.VerifyCodeScene_MY_PHONE {
		// 验证登录手机号
		uid, err := service.Session().GetUserIdByToken(ctx)
		if err != nil || uid <= 0 {
			return nil, err
		}

		err = service.Verify().SendMyPhoneCode(ctx, uid)
		if err != nil {
			g.Log().Error(ctx, err)
			return nil, err
		}
	} else if req.VerifyCodeScene == v1.VerifyCodeScene_MY_EMAIL {
		// 发送验证码到“我的邮箱”
		err = c.sendCodeMyEmail(ctx)
	} else if req.VerifyCodeScene == v1.VerifyCodeScene_RESET_PASSWORD {
		// 重置密码
	} else {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter)
	}

	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	res = &v1.SendVerifyCodeRes{
		Code: 200,
		Msg:  "success",
	}
	return res, nil
}

// 发送验证码到“我的邮箱”
func (*Controller) sendCodeMyEmail(ctx context.Context) (err error) {
	// 验证登录手机号
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil || uid <= 0 {
		return err
	}

	err = service.Verify().SendMyEmailCode(ctx, uid)
	if err != nil {
		return err
	}

	return nil
}

func (*Controller) VerifyCode(ctx context.Context, req *v1.VerifyCodeReq) (res *v1.VerifyCodeRes, err error) {
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	if g.IsEmpty(req.GetOptCode()) {
		return nil, errno.T(ctx, gcode.CodeInvalidParameter)
	}
	var ok = false
	switch req.GetVerifyCodeScene() {
	case v1.VerifyCodeScene_MY_PHONE:
		ok, err = service.Verify().VerifyMyPhoneCode(ctx, uid, req.GetOptCode())
		break
	case v1.VerifyCodeScene_MY_EMAIL:
		ok, err = service.Verify().VerifyMyEmailCode(ctx, uid, req.GetOptCode())
		if err == nil && ok {
			rt := &model.ResetToken{
				UserId: uid,
			}
			service.Verify().SetResetToken(ctx, rt)
		}
		break
	default:
		ok = false
	}

	if err != nil {
		return nil, errno.Te(ctx, errno.CodeOptCodeError, err)
	}
	if !ok {
		return nil, errno.T(ctx, errno.CodeOptCodeError)
	}
	return &v1.VerifyCodeRes{
		Code: 200,
		Msg:  "success",
	}, nil
}
