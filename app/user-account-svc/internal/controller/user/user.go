package user

import (
	"context"
	v1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/app/user-account-svc/internal/consts"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/service"

	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type Controller struct {
	v1.UnimplementedUserServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterUserServiceServer(s.Server, &Controller{})
}

func (*Controller) SignUp(ctx context.Context, req *v1.SignUpReq) (res *v1.SignUpRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) SignInByAccount(ctx context.Context, req *v1.SignInByAccountReq) (res *v1.SignInByAccountRes, err error) {
	//  grpcurl -plaintext -d '{}' 127.0.0.1:9200 user.v1.UserService/SignInByAccount
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) RefreshToken(ctx context.Context, req *v1.RefreshTokenReq) (res *v1.RefreshTokenRes, err error) {
	// get header
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	res = &v1.RefreshTokenRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.RefreshTokenResData{},
	}

	if headerMap.Contains(consts.HttpSessionId) {
		sessionId := gconv.String(headerMap.Get(consts.HttpSessionId))
		frontInfo := &model.FrontInfo{}
		gconv.Struct(req.FrontInfo, frontInfo)
		token, err := service.Session().RefreshJwtToken(ctx, sessionId, frontInfo)
		if err != nil {
			g.Log().Error(ctx, err)
		}
		res.Data.Token = token
	}

	return res, nil
}

func (c *Controller) UserInfo(ctx context.Context, req *v1.UserInfoReq) (res *v1.UserInfoRes, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	userModel, err := service.User().GetUserInfo(ctx, uid)
	res = &v1.UserInfoRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UserInfoResData{
			UserInfo: c.fillUserInfo(userModel),
		},
	}
	return res, nil
}

func (*Controller) UpdateUserInfo(ctx context.Context, req *v1.UpdateUserInfoReq) (res *v1.UpdateUserInfoRes, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	updatedAttr := do.UserInfo{}
	if !g.IsEmpty(req.Nickname) {
		updatedAttr.Nickname = req.Nickname
	}
	if !g.IsEmpty(req.Avatar) {
		updatedAttr.Avatar = req.Avatar
	}
	if !g.IsEmpty(req.Gender) && req.Gender.Number() > 0 {
		updatedAttr.Gender = req.Gender.Number()
	}
	if !g.IsEmpty(req.FirstName) {
		updatedAttr.FirstName = req.FirstName
	}
	if !g.IsEmpty(req.MiddleName) {
		updatedAttr.MiddleName = req.MiddleName
	}
	if !g.IsEmpty(req.LastName) {
		updatedAttr.LastName = req.LastName
	}
	err = dao.UserInfo.UpateUserInfo(ctx, uid, updatedAttr)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	return &v1.UpdateUserInfoRes{
		Code: 200,
		Msg:  "success",
	}, nil
}

func (*Controller) AvatarList(ctx context.Context, req *v1.AvatarListReq) (res *v1.AvatarListRes, err error) {
	items, err := dao.UserAvatar.GetDefaultList(ctx)
	if err != nil {
		return nil, errno.T(ctx, errno.CodeRecodeNotExist)
	}
	var list = make([]*v1.AvatarItem, len(items))
	for i, item := range items {
		list[i] = &v1.AvatarItem{
			Name: item.Name,
			Key:  item.AvatarKey,
			Url:  item.Url,
		}
	}
	return &v1.AvatarListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.AvatarListData{
			List: list,
		},
	}, nil
}

// 填写v1.UserInfo给api返回
func (*Controller) fillUserInfo(info *model.User) *v1.UserInfo {
	ui := &v1.UserInfo{
		Id:           info.Id,
		BindPhone:    !g.IsEmpty(info.PhoneNum),
		BindRealName: !g.IsEmpty(info.FirstName), // 实名认证
		Gender:       v1.Gender(gconv.Int32(info.Gender)),
		Avatar:       info.Avatar,
		Nickname:     info.Nickname,
		PhoneNum:     info.PhoneNum,
		AreaCode:     info.AreaCode,
		FirstName:    info.FirstName,
		MiddleName:   info.MiddleName,
		LastName:     info.LastName,
		IsGuest:      info.IsGuest,
	}

	if !g.IsEmpty(info.Email) {
		ui.BindEmail = true
		ui.Email = info.Email
	}

	return ui
}
