package user

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/test/gtest"
	"halalplus/api/common"
	userv1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/utility"
	"testing"
	"time"
)

var userServiceClient userv1.UserServiceClient
var ctx context.Context
var reqCtx context.Context
var frontInfo *common.FrontInfo

func TestMain(m *testing.M) {
	ctx = gctx.GetInitCtx()
	conn := grpcx.Client.MustNewGrpcClientConn("192.168.64.1:9200")
	userServiceClient = userv1.NewUserServiceClient(conn)
	frontInfo = &common.FrontInfo{
		DeviceId:        "4ed98548-d260-4be8-b380-ac3d38351036",
		DeviceOs:        "mac",
		AppVersion:      "v0.0.0",
		AppType:         5,
		DeviceOsVersion: "Version 15.5",
	}
	reqCtx = grpcx.Ctx.NewOutgoing(ctx, g.Map{
		"Authorization":    "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************.FBDxdjhXz5a8kowdc22Io_P9rvrpHtTtkAPbt8wZyXM",
		"X-Session-Id":     "W8ECGLT0pjEaZVrbxwFPRfeq",
		"X-Request-Id":     utility.GenerateRandomString(36),
		"X-Timestamp":      time.Now().Unix(),
		"X-Signature":      "xxxx",
		"X-Sign-Verersion": "v1",
	})
	m.Run()
}

func TestController_RefreshToken(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		reqCtx := grpcx.Ctx.NewOutgoing(ctx, g.Map{
			"X-Session-Id":     "cCLbTwnxlfDRzQzXuavNG6GR",
			"X-Request-Id":     utility.GenerateRandomString(36),
			"X-Timestamp":      time.Now().Unix(),
			"X-Signature":      "xxxx",
			"X-Sign-Verersion": "v1",
		})
		req := &userv1.RefreshTokenReq{
			FrontInfo: frontInfo,
		}
		res, err := userServiceClient.RefreshToken(reqCtx, req)
		if err != nil {
			t.Error(ctx, err)
		}
		t.Assert(res.Code, "200")
		t.Log(res)

		reqCtx = grpcx.Ctx.NewOutgoing(ctx, g.Map{
			"Authorization":    "Bearer " + res.Data.Token,
			"X-Session-Id":     "BrxxMJGRCB3xE5r55QEhK7vs",
			"X-Request-Id":     utility.GenerateRandomString(36),
			"X-Timestamp":      time.Now().Unix(),
			"X-Signature":      "xxxx",
			"X-Sign-Verersion": "v1",
		})
		req2 := &userv1.UserInfoReq{}
		res2, err := userServiceClient.UserInfo(reqCtx, req2)
		if err != nil {
			t.Error(ctx, err)
		}
		t.Log(res2)
	})
}

func TestController_UserInfo(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		req := &userv1.UserInfoReq{}
		res, err := userServiceClient.UserInfo(reqCtx, req)
		if err != nil {
			t.Error(ctx, err)
		}
		t.Log(res)
		t.Assert(res.Code, "200")
	})
}

func TestController_UpdateUserInfo(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		reqCtx := grpcx.Ctx.NewOutgoing(ctx, g.Map{
			"Authorization":    "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0eSI6InVzZXIiLCJzdWIiOiI1IiwiZXhwIjoxNzUzMjQzNzc0fQ.XVtLrE6CtyrZ0E-jxm7Ks5p4kPUog6H1W1O6de8HOwE",
			"X-Session-Id":     "BPKjBq7swSsj1RHxdLUnADdg",
			"X-Request-Id":     utility.GenerateRandomString(36),
			"X-Timestamp":      time.Now().Unix(),
			"X-Signature":      "xxxx",
			"X-Sign-Verersion": "v1",
		})
		req := &userv1.UpdateUserInfoReq{
			Nickname: "adrian2",
			Avatar:   "default",
			Gender:   1,
		}
		res, err := userServiceClient.UpdateUserInfo(reqCtx, req)
		if err != nil {
			t.Error(ctx, err)
		}
		t.Log(res)
		t.Assert(res.Code, "200")
	})
}
