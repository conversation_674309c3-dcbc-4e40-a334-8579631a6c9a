package user

import (
	"github.com/gogf/gf/v2/test/gtest"
	userv1 "halalplus/app/user-account-svc/api/user/v1"
	"testing"
)

// 检查邮箱是否被注册
func TestController_EmailValidCheck(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.EmailValidCheck(ctx, &userv1.EmailValidCheckReq{
			Email: "<EMAIL>",
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.EmailValidCheck(ctx, &userv1.EmailValidCheckReq{
			Email: "<EMAIL>",
		})
		t.Log(res)
		t.<PERSON>ser<PERSON>(err, nil)
		t.<PERSON><PERSON><PERSON>(res.Code, "15017")
	})
}

// 邮箱注册登录
func TestController_SignInByEmail(t *testing.T) {
	email := "<EMAIL>"

	// 检查邮箱是否注册
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.EmailValidCheck(ctx, &userv1.EmailValidCheckReq{
			Email: email,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})

	// 发送验证码
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SendVerifyCode(ctx, &userv1.SendVerifyCodeReq{
			Email:             email,
			VerifyCodeScene:   userv1.VerifyCodeScene_LOGIN,
			VerifyCodeChannel: userv1.VerifyCodeChannel_EMAIL,
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "200")
	})

	// 邮箱验证码登录/注册
	gtest.C(t, func(t *gtest.T) {
		res, err := userServiceClient.SignInByEmail(ctx, &userv1.SignInByEmailReq{
			Email:     email,
			FrontInfo: frontInfo,
			OptCode:   "123456",
		})
		t.Log(res)
		t.Assert(err, nil)
		t.Assert(res.Code, "15017")
	})
}
