package user

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
)

func (c *Controller) GuestSignIn(ctx context.Context, req *v1.GuestSignInReq) (res *v1.GuestSignInRes, err error) {
	if g.<PERSON>Empty(req.FrontInfo) || len(req.FrontInfo.DeviceId) < 30 {
		return nil, errno.T(ctx, errno.CodeUserInvalidParameter)
	}

	// 获取设备信息
	frontInfoModel := &model.FrontInfo{}
	gconv.Scan(req.FrontInfo, frontInfoModel)

	userModel, token, err := service.User().GuestSignIn(ctx, frontInfoModel)
	if err != nil {
		return nil, err
	}

	g.Log().Debug(ctx, userModel)
	res = &v1.GuestSignInRes{
		Code: 200,
		Msg:  "登录成功",
		Data: &v1.UserSignInResData{
			Token:     token,
			SessionId: userModel.SessionId,
			Secret:    userModel.SecretKey,
			UserInfo:  c.fillUserInfo(userModel),
		},
	}

	return res, nil
}
