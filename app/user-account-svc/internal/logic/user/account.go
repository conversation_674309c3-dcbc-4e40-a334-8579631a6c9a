package user

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"halalplus/app/user-account-svc/internal/consts"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
	"time"
)

const keyRegisterIp = "user:register:ip" // 注册时的IP
// CreateByPhone 用户注册（手机号）
func (s *sUser) CreateByPhone(ctx context.Context, in *model.SignUpByPhoneReq) (userId uint64, err error) {
	hashPassword := ""
	if !g.IsEmpty(in.Password) {
		hashPassword, err = service.Utility().HashPassword(in.Password)
		if err != nil {
			g.Log().Error(ctx, err)
			return
		}
	}

	g.Log().Debug(ctx, in)
	usr := s.NewUser(ctx, &in.FrontInfo, in.OriginalDomain)
	usr.Account = in.Account
	usr.AreaCode = in.AreaCode
	usr.PhoneNum = in.PhoneNum
	usr.Password = hashPassword

	userId, err = s.createUser(ctx, usr)
	if err != nil {
		g.Log().Error(ctx, err)
	}

	return
}

// CreateByEmail 用户注册（邮箱）
func (s *sUser) CreateByEmail(ctx context.Context, email string, info *model.FrontInfo) (userId uint64, err error) {
	usr := s.NewUser(ctx, info, "")
	usr.Account = nil
	usr.AreaCode = nil
	usr.PhoneNum = nil
	usr.Password = nil
	usr.Email = email
	userId, err = s.createUser(ctx, usr)
	if err != nil {
		g.Log().Error(ctx, err)
	}

	return userId, nil
}

// 创建游客账号
func (s *sUser) CreateGuest(ctx context.Context, info *model.FrontInfo) (userId uint64, err error) {
	usr := s.NewUser(ctx, info, "")
	usr.Account = info.GetGuestAccount()
	usr.IsGuest = true
	userId, err = s.createUser(ctx, usr)
	if err != nil {
		g.Log().Error(ctx, err)
	}

	return userId, nil
}

func (s *sUser) NewUser(ctx context.Context, in *model.FrontInfo, originalDomain string) (ret do.User) {
	ret = do.User{
		Password:         nil,
		AreaCode:         nil,
		PhoneNum:         nil,
		IsBanned:         consts.UserStatusNormal,
		CreateTime:       time.Now().UnixMilli(),
		UpdateTime:       time.Now().UnixMilli(),
		SecurityPassword: "",
	}
	return
}

// 获取model.User
func (s *sUser) ComposeUser(ctx context.Context, user *entity.User) (userModel *model.User) {
	userInfo, _ := dao.UserInfo.GetInfo(ctx, user.Id)
	userModel = &model.User{
		Id:           user.Id,
		Account:      user.Account,
		PhoneNum:     userInfo.PhoneNum,
		Email:        user.Email,
		Address:      userInfo.Address,
		FirstName:    userInfo.FirstName,
		MiddleName:   userInfo.MiddleName,
		LastName:     userInfo.LastName,
		CreateTime:   user.CreateTime,
		CountryId:    userInfo.CountryId,
		Language:     userInfo.Language,
		Nickname:     userInfo.Nickname,
		Avatar:       userInfo.Avatar,
		YearOfBirth:  userInfo.YearOfBirth,
		MonthOfBirth: userInfo.MonthOfBirth,
		DayOfBirth:   userInfo.DayOfBirth,
		Gender:       userInfo.Gender,
		IsTest:       user.IsTest,
		IsGuest:      user.IsGuest == 1,
	}

	if g.IsEmpty(userModel.Language) {
		userModel.Language = consts.DefaultLanguage
	}

	return
}

// createUser 新建用户&新建后的各种初始化
func (s *sUser) createUser(ctx context.Context, usr do.User) (userId uint64, err error) {
	// -------- 创建事务
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		unixMilli := time.Now().UnixMilli()
		// 创建账号
		var temId int64
		usr.CreateTime = unixMilli
		temId, err = dao.User.Ctx(ctx).TX(tx).Data(usr).InsertAndGetId()
		if err != nil {
			return err
		}

		userId = gconv.Uint64(temId)

		ui := entity.UserInfo{
			UserId:           userId,
			Avatar:           dao.UserAvatar.GetRandom(ctx),
			Nickname:         utility.GenerateRandomString(5),
			CreateTime:       unixMilli,
			IdentityCardImgs: "[]",
		}

		if !g.IsEmpty(usr.PhoneNum) {
			ui.BindPhoneTime = unixMilli
			mp := &model.PhoneInfo{
				PhoneNum: gconv.String(usr.PhoneNum),
				AreaCode: gconv.String(usr.AreaCode),
			}
			ui.PhoneNum = mp.Key()
		} else {
			ui.BindPhoneTime = 0
		}

		if !g.IsEmpty(usr.Email) {
			ui.BindEmailTime = unixMilli
			ui.Email = gconv.String(usr.Email)
		}
		_, err = dao.UserInfo.Ctx(ctx).TX(tx).Insert(ui)
		if err != nil {
			return err
		}

		//service.Task().OnRegister(ctx, userId)
		return nil
	})

	return
}

func (s *sUser) ChangePhoneNumber(ctx context.Context, uid uint64, mp *model.PhoneInfo) (err error) {
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.User.Ctx(ctx).TX(tx).Where(dao.User.Columns().Id, uid).Update(do.User{
			PhoneNum: mp.GetPhoneNum(),
			AreaCode: mp.GetAreaCode(),
		})
		if err != nil {
			return err
		}
		_, err = dao.UserInfo.Ctx(ctx).TX(tx).Where(dao.UserInfo.Columns().UserId, uid).Update(do.UserInfo{
			PhoneNum: mp.Key(),
		})
		if err != nil {
			g.Log().Error(ctx, err)
			return err
		}
		return nil
	})
	if err != nil {
		g.Log().Error(ctx, err)
		return err
	}
	return nil
}
