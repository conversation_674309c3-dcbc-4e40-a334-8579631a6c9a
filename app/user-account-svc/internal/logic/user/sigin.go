package user

import (
	"context"
	"database/sql"
	"errors"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
)

var cl = dao.User.Columns()

func (s *sUser) SignInByPhoneNum(ctx context.Context, areaCode string, phoneNum string, frontInfo *model.FrontInfo) (userModel *model.User, token string, err error) {
	// TODO: LoginFrequencyControl(areaCode+phoneNum)
	user, err := dao.User.GetUserEntityByPhone(ctx, areaCode, phoneNum)
	if err != nil {
		g.Log().Error(ctx, err)

		// TODO： 创建用户
		signUpByPhoneReq := &model.SignUpByPhoneReq{
			FrontInfo: *frontInfo,
			Account:   areaCode + phoneNum,
			Password:  utility.GenerateRandomString(12),
			PhoneInfo: model.PhoneInfo{
				PhoneNum: phoneNum,
				AreaCode: areaCode,
			},
			AgentCode:      "",
			InviteCode:     "",
			Lang:           "",
			OptCode:        "",
			OriginalDomain: "",
			PixelId:        "",
			RegisterUrl:    "",
			Fbclid:         "",
			FbTimeStamp:    0,
			Esu:            "",
		}
		uid, err2 := s.CreateByPhone(ctx, signUpByPhoneReq)
		if err2 != nil {
			g.Log().Error(ctx, err2)
			return
		}

		err = dao.User.Ctx(ctx).Master().Where(cl.Id, uid).Scan(&user)
		if err != nil {
			g.Log().Error(ctx, err)
			return
		}
	}
	if user == nil {
		g.Log().Error(ctx, "user nil")
		return
	}

	return s.commonLoginSuccess(ctx, user, frontInfo)
}

func (s *sUser) commonLoginSuccess(ctx context.Context, user *entity.User, frontInfo *model.FrontInfo) (userModel *model.User, token string, err error) {

	if user.IsBanned != 1 {
		return nil, "", errno.T(ctx, errno.CodeUserLoginBen)
	}

	// 创建会话
	userSession, err := service.Session().CreateUserSession(ctx, user, frontInfo)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, "", err
	}

	userModel = s.ComposeUser(ctx, user)
	userModel.SessionId = userSession.SessionId
	userModel.SecretKey = userSession.SecretKey

	s.PushSignInLog(ctx, user, frontInfo)

	token, err = service.Session().GenJwtToken(ctx, &model.UserSession{
		UserId:    userModel.Id,
		SessionId: userModel.SessionId,
	})

	if user.IsGuest == 1 {
		userModel.IsGuest = true
	}

	return userModel, token, err
}

func (s *sUser) SignInByEmailPass(ctx context.Context, email string, pass string, frontInfo *model.FrontInfo) (userModel *model.User, token string, err error) {
	user, err := dao.User.GetUserEntityByEmail(ctx, email)
	if err != nil {
		g.Log().Error(ctx, err)
		if errors.Is(err, sql.ErrNoRows) {
			return nil, "", errno.T(ctx, errno.CodeUserNotFoundError)
		}
		return nil, "", errno.T(ctx, gerror.Code(err))
	}

	// 判断密码
	validPass := service.Utility().CheckPasswordHash(pass, user.Password)
	if !validPass {
		g.Log().Info(ctx, "email password login failed", user.Id, user.Email)
		return nil, "", errno.T(ctx, errno.CodeUserAccountPasswordError)
	}

	return s.commonLoginSuccess(ctx, user, frontInfo)
}

func (s *sUser) SignInByEmail(ctx context.Context, email string, frontInfo *model.FrontInfo) (userModel *model.User, token string, err error) {
	user, err := dao.User.GetUserEntityByEmail(ctx, email)
	if err != nil {

		if errors.Is(err, sql.ErrNoRows) {
			// 创建用户
			g.Log().Info(ctx, "sql.ErrNoRows 创建用户")
			var userId uint64
			userId, err = s.CreateByEmail(ctx, email, frontInfo)
			if err != nil {
				g.Log().Error(ctx, err)
				return nil, "", err
			}
			err = dao.User.Ctx(ctx).Master().Where(cl.Id, userId).Scan(&user)
			if err != nil {
				g.Log().Error(ctx, err)
				return
			}
		} else {
			g.Log().Error(ctx, err)
		}
	}

	if user == nil || err != nil {
		return nil, "", errno.Te(ctx, errno.CodeUserNotFoundError, err)
	}

	return s.commonLoginSuccess(ctx, user, frontInfo)
}

// 游客登录
func (s *sUser) GuestSignIn(ctx context.Context, frontInfo *model.FrontInfo) (userModel *model.User, token string, err error) {
	// TODO： 登录上锁
	user, err := dao.User.GetUserEntityByAccount(ctx, frontInfo.GetGuestAccount(), true)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// 创建用户
			g.Log().Info(ctx, "sql.ErrNoRows 创建游客用户")
			var userId uint64
			userId, err = s.CreateGuest(ctx, frontInfo)
			if err != nil {
				g.Log().Error(ctx, err)
				return nil, "", err
			}
			err = dao.User.Ctx(ctx).Master().Where(cl.Id, userId).Scan(&user)
			if err != nil {
				g.Log().Error(ctx, err)
				return
			}
		} else {
			g.Log().Error(ctx, err)
		}
	}

	if user == nil || err != nil {
		return nil, "", errno.Te(ctx, errno.CodeUserNotFoundError, err)
	}

	return s.commonLoginSuccess(ctx, user, frontInfo)
}
