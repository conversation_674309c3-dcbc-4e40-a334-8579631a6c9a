package user

import (
	"context"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/errno"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility/bloom"
)

type (
	sUser struct {
		signInMsgChan        chan *model.SignInLogInput
		attrsBatchUpdateChan chan *model.AttrsToUpdate
		attrsNoDelayChan     chan *model.AttrsToUpdate
		quit                 chan struct{}

		ConfigCaptcha    string
		signInRecordChan chan *do.UserSigninLog
		accountSet       *bloom.Filter
		transferSet      *bloom.Filter
		phoneSet         *bloom.Filter
	}
)

func init() {
	service.RegisterUser(New())
}

func New() service.IUser {
	u := &sUser{
		signInMsg<PERSON>han:        make(chan *model.SignInLogInput, 1000),
		attrsBatchUpdateChan: make(chan *model.AttrsToUpdate, 10000),
		attrsNoDelayChan:     make(chan *model.AttrsToUpdate, 100),

		signInRecordChan: make(chan *do.UserSigninLog, 1000),
		quit:             make(chan struct{}, 1),
	}

	return u
}

// IsSignedIn checks and returns whether current user is already signed-in.
func (s *sUser) IsSignedIn(ctx context.Context) bool {
	return true
}

func (s *sUser) IsPhoneAccountExist(ctx context.Context, phoneNum string, account string) (isUnique bool, err error) {
	// if len(phoneNum) == 0 {
	//	return true, nil
	// }
	//
	oneRes, err := dao.User.Ctx(ctx).Fields(cl.Account, cl.PhoneNum).Where(cl.Account, account).WhereOr(cl.PhoneNum, phoneNum).One()
	if oneRes[cl.Account].String() == account {
		return false, errno.T(ctx, errno.CodeUserAccountExisted)
	}
	if oneRes[cl.PhoneNum].String() == phoneNum {
		return false, errno.T(ctx, errno.CodeUserPhoneNumberExisted)
	}
	return true, nil
}

// GetUserInfo 获取用户信息
func (s *sUser) GetUserInfo(ctx context.Context, uid uint64) (userInfo *model.User, err error) {
	usr, err := dao.User.GetUserEntityById(ctx, uid)
	if err != nil {
		return nil, err
	}
	return s.ComposeUser(ctx, usr), nil
}
