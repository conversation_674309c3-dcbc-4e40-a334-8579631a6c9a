// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// User is the golang structure of table user for DAO operations like Where/Data.
type User struct {
	g.Meta           `orm:"table:user, do:true"`
	Id               interface{} //
	Account          interface{} // 账号，如果是游客则账号是设备号
	Email            interface{} // 登录邮箱
	Password         interface{} // 密码
	AreaCode         interface{} // 手机国际区号，如：86
	PhoneNum         interface{} // 手机号
	IsBanned         interface{} // 账号封号状态： 1 正常 2 封号
	SecurityPassword interface{} // 安全密码，修改个人绑定信息时要验证
	IsTest           interface{} // 测试账号：  1 是 ，其他值：否
	IsGuest          interface{} // 游客：  1 是 ，其他值：否
	CreateTime       interface{} // 创建时间（注册时间）
	UpdateTime       interface{} // 更新时间，0代表创建后未被修改过
}
