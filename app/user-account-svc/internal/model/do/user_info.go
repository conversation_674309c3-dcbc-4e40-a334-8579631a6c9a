// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserInfo is the golang structure of table user_info for DAO operations like Where/Data.
type UserInfo struct {
	g.Meta               `orm:"table:user_info, do:true"`
	Id                   interface{} //
	UserId               interface{} // 关联 user 表主键
	IsGuest              interface{} // 是否是游客
	Email                interface{} // 邮箱地址
	BindEmailTime        interface{} // 邮箱绑定时间
	BindRealNameTime     interface{} // 真实姓名绑定时间
	BindPhoneTime        interface{} // 手机号绑定时间
	PhoneNum             interface{} // 手机号
	PasswordModifyTime   interface{} // 登录密码支付密码最近修改时间
	CountryId            interface{} // 国家id
	Language             interface{} // 语言 : zh-CN:中文, id:Indonesian, en:English
	YearOfBirth          interface{} // 出生年
	MonthOfBirth         interface{} // 出生月
	DayOfBirth           interface{} // 出生日
	Avatar               interface{} // 头像url
	Gender               interface{} // 性别：0未知 1男 2女
	Nickname             interface{} // 昵称
	NicknameModifyTime   interface{} // 昵称最近一次修改时间
	FirstName            interface{} // 第一个名字
	MiddleName           interface{} // 中间名字
	LastName             interface{} // 最后一个名字
	Version              interface{} // 该记录的版本号
	IdentityCard         interface{} // 身份证号码
	IdentityCardImgs     interface{} // 身份证图片
	Contact              interface{} // 社交联系方式 wechat qq等
	Address              interface{} // 住址
	CreateAccount        interface{} // 创建者账号
	CreateType           interface{} // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount        interface{} // 更新者账号
	UpdateType           interface{} // 更新者来源
	DataType             interface{} // 数据类型:1正式数据;2测试数据
	Source               interface{} // 注册来源( 1直客，2代理，3邀请，4后台）
	IsOnline             interface{} // 是否在线：1是  2 否
	SigninCount          interface{} // 登录次数
	LastSigninTime       interface{} // 最后一次登录时间
	LastSigninIp         interface{} // 最后登录ip
	LastSigninDeviceId   interface{} // 最后登录设备号
	LastSigninAppType    interface{} // 最近登录应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	LastSigninAppVersion interface{} // 最近登录应用类型版本号
	CreateTime           interface{} // 创建时间
	UpdateTime           interface{} // 更新时间
}
