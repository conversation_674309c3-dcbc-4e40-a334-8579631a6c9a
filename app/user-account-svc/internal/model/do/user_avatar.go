// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserAvatar is the golang structure of table user_avatar for DAO operations like Where/Data.
type UserAvatar struct {
	g.Meta     `orm:"table:user_avatar, do:true"`
	Id         interface{} // 主键ID
	Name       interface{} // 文件名
	AvatarKey  interface{} // 对象存储Key
	Url        interface{} // 完整访问URL
	CreateTime interface{} // 创建时间（注册时间）
	UpdateTime interface{} // 更新时间，0代表创建后未被修改过
}
