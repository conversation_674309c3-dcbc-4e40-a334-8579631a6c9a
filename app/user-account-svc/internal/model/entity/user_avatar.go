// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// UserAvatar is the golang structure for table user_avatar.
type UserAvatar struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`             // 主键ID
	Name       string `json:"name"       orm:"name"        description:"文件名"`              // 文件名
	AvatarKey  string `json:"avatarKey"  orm:"avatar_key"  description:"对象存储Key"`          // 对象存储Key
	Url        string `json:"url"        orm:"url"         description:"完整访问URL"`          // 完整访问URL
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间（注册时间）"`       // 创建时间（注册时间）
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"更新时间，0代表创建后未被修改过"` // 更新时间，0代表创建后未被修改过
}
