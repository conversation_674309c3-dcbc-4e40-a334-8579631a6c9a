package model

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"github.com/google/uuid"
	"halalplus/utility/slices"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
)

// 认证
type Author struct {
	Authorization string `p:"Authorization" in:"header" dc:"Bearer {{token}}"`
}

// ListReq 分页请求参数
type ListReq struct {
	Current  int    `v:"required" json:"current" d:"1" dc:"当前页码"`                                    // 当前页码
	PageSize int    `v:"required|integer|between:1,200" json:"pageSize" d:"15" dc:"每页数量，默认15，最大200"` // 每页数
	OrderBy  string `json:"orderBy" dc:"排序方式：如 \"order_by desc,id desc\"，具体以实际业务为主"`                 // 排序方式
}

type ListPager struct {
	Current  int `v:"required|min:1" json:"current" d:"1" dc:"当前页码"`                              // 当前页码
	PageSize int `v:"required|integer|between:1,200" json:"pageSize" d:"15" dc:"每页数量，默认15，最大200"` // 每页数
}

// ListRes 列表公共返回
type ListRes struct {
	Current int `json:"current" dc:"当前页码"`
	Total   int `json:"total" dc:"总条数"`
}

// FrontInfo 前端信息采集
type FrontInfo struct {
	DeviceId        string `v:"required" json:"deviceId" dc:"设备号（设备指纹）"`
	DeviceOs        string `v:"required" json:"deviceOs" dc:"设备操作系统（android,ios,windows,mac,...）"`
	DeviceOsVersion string `json:"deviceOsVersion" dc:"设备操作系统版本号"`
	DeviceType      int    `v:"required|in:1,2,3,4" json:"deviceType" dc:"设备类型（1:mobile手机,2:desktop台式,3:pad平板，4:其他）"`
	AppType         int    `v:"required|in:1,2,3,4,5" json:"appType" dc:"应用类型（1:android 2:ios，3:h5，4:web，5:其他）"`
	AppVersion      string `v:"required" json:"appVersion" dc:"应用版本号"`
}

// 获取游客账号名
func (f *FrontInfo) GetGuestAccount() string {
	acct := strings.TrimSpace(f.DeviceId)
	if len(acct) == 0 {
		return uuid.New().String()
	}
	if len(acct) < 36 {
		tmp := fmt.Sprintf("%s%d", f.DeviceId, f.AppType)
		hash := sha256.Sum256([]byte(tmp))
		acct = hex.EncodeToString(hash[:])[:36] // 固定36字符
	}
	return acct
}

type OptCodeCommon struct {
	OptCode string `v:"required|integer|length:3,10" json:"optCode" dc:"验证码"`
}

type PhoneInfo struct {
	AreaCode string `v:"integer|length:2,3" json:"areaCode" dc:"国家码" d:"62"`
	PhoneNum string `v:"required|integer|length:8,13" json:"phoneNum" dc:"手机号"`
}

// GetAreaCode 区号格式化
func (s *PhoneInfo) GetAreaCode() string {
	if len(s.AreaCode) == 0 {
		// 默认值
		s.AreaCode = "62"
	}
	if gstr.Contains(s.AreaCode, "+") {
		if len(s.AreaCode) > 0 && s.AreaCode[0] == '+' {
			s.AreaCode = s.AreaCode[1:]
		}
	}
	return s.AreaCode
}

// GetPhoneNum 号码格式化
func (s *PhoneInfo) GetPhoneNum() string {
	// 对于印尼 手机前缀有0要去掉
	if gstr.Contains(s.GetAreaCode(), "62") {
		// 检查首字符是否为 '0'
		if len(s.PhoneNum) > 0 && s.PhoneNum[0] == '0' {
			// 返回去掉首字符后的子字符串
			s.PhoneNum = s.PhoneNum[1:]
		}
	}
	return s.PhoneNum
}

func (s *PhoneInfo) IsValid() bool {
	if len(s.GetAreaCode()) < 4 && len(s.GetPhoneNum()) > 8 {
		return true
	}
	return false
}

func (s *PhoneInfo) Key() string {
	return s.GetAreaCode() + s.GetPhoneNum()
}

type OrderByItem struct {
	Field string `v:"required" json:"field" dc:"排序字段"`
	Sort  string `v:"required|in:asc,desc" json:"sort" dc:"升序降序"`
}

type OrderByItems []OrderByItem

func (s OrderByItems) String() string {
	if s.Len() < 1 {
		return ""
	}
	data := bytes.Buffer{}
	for i, row := range s {
		if i > 0 {
			data.WriteString(",")
		}
		data.WriteString(row.Field + " " + strings.ToUpper(row.Sort))

	}
	return data.String()
}

func (s OrderByItems) Len() int {
	return len(s)
}

func (s OrderByItems) Build(m *gdb.Model) *gdb.Model {
	m = m.Order(s.String())
	return m
}

// Field 保留指定的字段
func (s *OrderByItems) Field(field ...string) *OrderByItems {
	if s.Len() < 1 {
		return s
	}
	data := &OrderByItems{}
	for _, row := range *s {
		if slices.Contains(field, row.Field) {
			*data = append(*data, row)
		}
	}
	return data
}

func (s *OrderByItems) FieldAny(sourceField ...any) *OrderByItems {
	fields := make([]string, 0, len(sourceField))
	for _, field := range sourceField {
		fields = append(fields, gconv.String(field))
	}
	return s.Field(fields...)
}

type AddressInfo struct {
	Country  string `json:"country" dc:"国家"`
	Region   string `json:"region" dc:"地区/州/市"`
	Postal   string `json:"postal" dc:"邮政编码"`
	Address1 string `json:"address1" dc:"地址1"`
	Address2 string `json:"address2" dc:"地址2"`
}

// SignUpByPhoneReq 用户注册（手机）
type SignUpByPhoneReq struct {
	FrontInfo
	Account  string `json:"account" dc:"账号名称" v:"required|userAccount|passport|length:6,16"`
	Password string `v:"required|length:8,18|password2" json:"password" dc:"密码"`
	PhoneInfo
	AgentCode  string `json:"agentCode" dc:"代理推广码（选填）"`
	InviteCode string `json:"inviteCode" dc:"好友邀请码（选填）"`
	Lang       string `json:"lang" dc:"语言"`
	// OptCode   string `v:"required|integer|length:3,10" json:"optCode" dc:"验证码"`
	OptCode        string `v:"integer|length:3,10" json:"optCode" dc:"验证码"`
	OriginalDomain string `json:"orignDomain" dc:"原始域名"`
	PixelId        string `json:"pixelId"  dc:"像素ID"`
	RegisterUrl    string `json:"registerUrl"   dc:"像素：投放注册网址"`
	Fbclid         string `json:"fbclid"   dc:"像素：fb点击id"`
	FbTimeStamp    int64  `json:"fbTimeStamp"   dc:"像素：自存储时间点起的 UNIX 时间（以毫秒为单位)"`
	Esu            string `json:"esu"   dc:"像素：esu"`
}
