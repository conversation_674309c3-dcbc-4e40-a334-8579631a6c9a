// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"halalplus/app/user-account-svc/internal/dao/internal"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
)

// userInfoDao is the data access object for the table user_info.
// You can define custom methods on it to extend its functionality as needed.
type userInfoDao struct {
	*internal.UserInfoDao
}

var (
	// UserInfo is a globally accessible object for table user_info operations.
	UserInfo = userInfoDao{internal.NewUserInfoDao()}
)

// Add your custom methods and functionality below.
func (d *userInfoDao) GetInfo(ctx context.Context, userId uint64) (info *entity.UserInfo, err error) {
	info = &entity.UserInfo{}
	err = d.Ctx(ctx).Where(d.Columns().UserId, userId).Scan(&info)
	if err != nil {
		g.Log().Debug(ctx, "get user info err: ", err)
		return nil, err
	}
	return info, nil
}

func (d *userInfoDao) UpateUserInfo(ctx context.Context, uid uint64, updatedAttr do.UserInfo) (err error) {
	// Gender字段确保是字符串
	if !g.IsEmpty(updatedAttr.Gender) {
		updatedAttr.Gender = gconv.String(updatedAttr.Gender)
	}
	_, err = d.Ctx(ctx).Where(d.Columns().UserId, uid).Update(updatedAttr)
	if err != nil {
		g.Log().Error(ctx, err)
		return err
	}
	return nil
}
