// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserAvatarDao is the data access object for the table user_avatar.
type UserAvatarDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserAvatarColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserAvatarColumns defines and stores column names for the table user_avatar.
type UserAvatarColumns struct {
	Id         string // 主键ID
	Name       string // 文件名
	AvatarKey  string // 对象存储Key
	Url        string // 完整访问URL
	CreateTime string // 创建时间（注册时间）
	UpdateTime string // 更新时间，0代表创建后未被修改过
}

// userAvatarColumns holds the columns for the table user_avatar.
var userAvatarColumns = UserAvatarColumns{
	Id:         "id",
	Name:       "name",
	AvatarKey:  "avatar_key",
	Url:        "url",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewUserAvatarDao creates and returns a new DAO object for table data access.
func NewUserAvatarDao(handlers ...gdb.ModelHandler) *UserAvatarDao {
	return &UserAvatarDao{
		group:    "default",
		table:    "user_avatar",
		columns:  userAvatarColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserAvatarDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserAvatarDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserAvatarDao) Columns() UserAvatarColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserAvatarDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserAvatarDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserAvatarDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
