// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserInfoDao is the data access object for the table user_info.
type UserInfoDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserInfoColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserInfoColumns defines and stores column names for the table user_info.
type UserInfoColumns struct {
	Id                   string //
	UserId               string // 关联 user 表主键
	IsGuest              string // 是否是游客
	Email                string // 邮箱地址
	BindEmailTime        string // 邮箱绑定时间
	BindRealNameTime     string // 真实姓名绑定时间
	BindPhoneTime        string // 手机号绑定时间
	PhoneNum             string // 手机号
	PasswordModifyTime   string // 登录密码支付密码最近修改时间
	CountryId            string // 国家id
	Language             string // 语言 : zh-CN:中文, id:Indonesian, en:English
	YearOfBirth          string // 出生年
	MonthOfBirth         string // 出生月
	DayOfBirth           string // 出生日
	Avatar               string // 头像url
	Gender               string // 性别：0未知 1男 2女
	Nickname             string // 昵称
	NicknameModifyTime   string // 昵称最近一次修改时间
	FirstName            string // 第一个名字
	MiddleName           string // 中间名字
	LastName             string // 最后一个名字
	Version              string // 该记录的版本号
	IdentityCard         string // 身份证号码
	IdentityCardImgs     string // 身份证图片
	Contact              string // 社交联系方式 wechat qq等
	Address              string // 住址
	CreateAccount        string // 创建者账号
	CreateType           string // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount        string // 更新者账号
	UpdateType           string // 更新者来源
	DataType             string // 数据类型:1正式数据;2测试数据
	Source               string // 注册来源( 1直客，2代理，3邀请，4后台）
	IsOnline             string // 是否在线：1是  2 否
	SigninCount          string // 登录次数
	LastSigninTime       string // 最后一次登录时间
	LastSigninIp         string // 最后登录ip
	LastSigninDeviceId   string // 最后登录设备号
	LastSigninAppType    string // 最近登录应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	LastSigninAppVersion string // 最近登录应用类型版本号
	CreateTime           string // 创建时间
	UpdateTime           string // 更新时间
}

// userInfoColumns holds the columns for the table user_info.
var userInfoColumns = UserInfoColumns{
	Id:                   "id",
	UserId:               "user_id",
	IsGuest:              "is_guest",
	Email:                "email",
	BindEmailTime:        "bind_email_time",
	BindRealNameTime:     "bind_real_name_time",
	BindPhoneTime:        "bind_phone_time",
	PhoneNum:             "phone_num",
	PasswordModifyTime:   "password_modify_time",
	CountryId:            "country_id",
	Language:             "language",
	YearOfBirth:          "year_of_birth",
	MonthOfBirth:         "month_of_birth",
	DayOfBirth:           "day_of_birth",
	Avatar:               "avatar",
	Gender:               "gender",
	Nickname:             "nickname",
	NicknameModifyTime:   "nickname_modify_time",
	FirstName:            "first_name",
	MiddleName:           "middle_name",
	LastName:             "last_name",
	Version:              "version",
	IdentityCard:         "identity_card",
	IdentityCardImgs:     "identity_card_imgs",
	Contact:              "contact",
	Address:              "address",
	CreateAccount:        "create_account",
	CreateType:           "create_type",
	UpdateAccount:        "update_account",
	UpdateType:           "update_type",
	DataType:             "data_type",
	Source:               "source",
	IsOnline:             "is_online",
	SigninCount:          "signin_count",
	LastSigninTime:       "last_signin_time",
	LastSigninIp:         "last_signin_ip",
	LastSigninDeviceId:   "last_signin_device_id",
	LastSigninAppType:    "last_signin_app_type",
	LastSigninAppVersion: "last_signin_app_version",
	CreateTime:           "create_time",
	UpdateTime:           "update_time",
}

// NewUserInfoDao creates and returns a new DAO object for table data access.
func NewUserInfoDao(handlers ...gdb.ModelHandler) *UserInfoDao {
	return &UserInfoDao{
		group:    "default",
		table:    "user_info",
		columns:  userInfoColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserInfoDao) Columns() UserInfoColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserInfoDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
