// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/dao/internal"
	"halalplus/app/user-account-svc/internal/model/entity"
	"math/rand"
	"time"
)

// userAvatarDao is the data access object for the table user_avatar.
// You can define custom methods on it to extend its functionality as needed.
type userAvatarDao struct {
	*internal.UserAvatarDao
}

var (
	// UserAvatar is a globally accessible object for table user_avatar operations.
	UserAvatar = userAvatarDao{internal.NewUserAvatarDao()}
)

func (d *userAvatarDao) GetDefaultList(ctx context.Context) ([]*entity.UserAvatar, error) {
	var items []*entity.UserAvatar
	err := d.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: time.Hour,
	}).Limit(10).Scan(&items)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	return items, err
}

// 随机一个头像地址
func (d *userAvatarDao) GetRandom(ctx context.Context) string {
	items, err := d.GetDefaultList(ctx)
	if err != nil {
		return "default.png"
	}
	i := rand.Intn(len(items))
	return items[i].Url
}
