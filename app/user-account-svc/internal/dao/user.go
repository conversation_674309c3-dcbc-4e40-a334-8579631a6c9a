// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"halalplus/app/user-account-svc/internal/dao/internal"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
)

// userDao is the data access object for the table user.
// You can define custom methods on it to extend its functionality as needed.
type userDao struct {
	*internal.UserDao
}

var (
	// User is a globally accessible object for table user operations.
	User = userDao{internal.NewUserDao()}
)

func (d *userDao) GetUserEntityByPhone(ctx context.Context, areaCode string, phoneNum string) (user *entity.User, err error) {
	user = new(entity.User)
	err = d.Ctx(ctx).Where(d.Columns().AreaCode, areaCode).Where(d.Columns().PhoneNum, phoneNum).Scan(&user)
	if err != nil {
		return nil, err
	}
	if g.IsEmpty(user.Id) {
		return nil, errors.New("user is not exist")
	}
	return user, nil
}

func (d *userDao) GetUserEntityByEmail(ctx context.Context, email string) (user *entity.User, err error) {
	user = new(entity.User)
	err = d.Ctx(ctx).Where(d.Columns().Email, email).Scan(&user)
	if err != nil {
		return nil, err
	}
	if g.IsEmpty(user.Id) {
		return nil, errors.New("user is not exist")
	}
	return user, nil
}

func (d *userDao) GetUserEntityById(ctx context.Context, uid uint64) (user *entity.User, err error) {
	user = new(entity.User)
	err = d.Ctx(ctx).Where(d.Columns().Id, uid).Scan(&user)
	if err != nil {
		return nil, err
	}
	if g.IsEmpty(user.Id) {
		return nil, errors.New("user is not exist")
	}
	return user, nil
}

// 获取游客账号 GetUserEntityByAccount(ctx, deviceId, true)
func (d *userDao) GetUserEntityByAccount(ctx context.Context, account string, isGuest bool) (user *entity.User, err error) {
	user = new(entity.User)
	guest := 0
	if isGuest {
		guest = 1
	}
	err = d.Ctx(ctx).Where(d.Columns().Account, account).Where(d.Columns().IsGuest, guest).Scan(&user)
	if err != nil {
		return nil, err
	}
	if g.IsEmpty(user.Id) {
		return nil, errors.New("user is not exist")
	}
	return user, nil
}

func (d *userDao) SetPassword(ctx context.Context, uid uint64, hashPassword string) error {
	// 设置密码
	affected, err := d.Ctx(ctx).Master().
		Where(d.Columns().Id, uid).
		Data(do.User{
			Password: hashPassword,
		}).
		UpdateAndGetAffected()
	if err != nil {
		g.Log().Error(ctx, err)
		return err
	}
	if affected == 1 {
		return nil
	}
	return errors.New("failed to set password")
}

func (d *userDao) GetUser(ctx context.Context, uid uint64) (userModel *model.User, err error) {
	var userEntity = new(entity.User)
	err = d.Ctx(ctx).Where(d.Columns().Id, uid).Scan(&userEntity)
	if err != nil {
		g.Log().Error(ctx, "GetUser failed to query user", err)
		return nil, err
	}
	userModel = new(model.User)
	err = gconv.Struct(userEntity, userModel)
	if err != nil {
		g.Log().Error(ctx, "Struct to user model", err)
		return nil, err
	}
	return userModel, err
}

func (d *userDao) GetMyPHone(ctx context.Context, uid uint64) (mp *model.PhoneInfo, err error) {
	var usr = entity.User{}
	err = d.Ctx(ctx).Where(d.Columns().Id, uid).Fields(d.Columns().AreaCode, d.Columns().PhoneNum).Scan(&usr)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	mp = &model.PhoneInfo{
		AreaCode: usr.AreaCode,
		PhoneNum: usr.PhoneNum,
	}
	return mp, nil
}

func (d *userDao) GetMyEmail(ctx context.Context, uid uint64) (email string, err error) {
	// 获取用户邮箱
	emailVal, err := d.Ctx(ctx).Where(d.Columns().Id, uid).Value(d.Columns().Email)
	if err != nil || emailVal.IsEmpty() {
		g.Log().Error(ctx, err)
		return "", err
	}
	return emailVal.String(), nil
}
