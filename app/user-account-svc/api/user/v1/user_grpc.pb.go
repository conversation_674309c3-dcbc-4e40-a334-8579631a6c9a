// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: user/v1/user.proto

package v1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserService_GuestSignIn_FullMethodName     = "/user.v1.UserService/GuestSignIn"
	UserService_SendVerifyCode_FullMethodName  = "/user.v1.UserService/SendVerifyCode"
	UserService_VerifyCode_FullMethodName      = "/user.v1.UserService/VerifyCode"
	UserService_SignUp_FullMethodName          = "/user.v1.UserService/SignUp"
	UserService_SignIn_FullMethodName          = "/user.v1.UserService/SignIn"
	UserService_SignInByAccount_FullMethodName = "/user.v1.UserService/SignInByAccount"
	UserService_SignInByPhone_FullMethodName   = "/user.v1.UserService/SignInByPhone"
	UserService_SignInByEmail_FullMethodName   = "/user.v1.UserService/SignInByEmail"
	UserService_RefreshToken_FullMethodName    = "/user.v1.UserService/RefreshToken"
	UserService_UserInfo_FullMethodName        = "/user.v1.UserService/UserInfo"
	UserService_UpdateUserInfo_FullMethodName  = "/user.v1.UserService/UpdateUserInfo"
	UserService_AvatarList_FullMethodName      = "/user.v1.UserService/AvatarList"
	UserService_PhoneValidCheck_FullMethodName = "/user.v1.UserService/PhoneValidCheck"
	UserService_ChangePhone_FullMethodName     = "/user.v1.UserService/ChangePhone"
	UserService_EmailValidCheck_FullMethodName = "/user.v1.UserService/EmailValidCheck"
	UserService_ChangeEmail_FullMethodName     = "/user.v1.UserService/ChangeEmail"
	UserService_LoginGoogle_FullMethodName     = "/user.v1.UserService/LoginGoogle"
	UserService_SetPassword_FullMethodName     = "/user.v1.UserService/SetPassword"
	UserService_ChangePassword_FullMethodName  = "/user.v1.UserService/ChangePassword"
	UserService_ResetPassword_FullMethodName   = "/user.v1.UserService/ResetPassword"
)

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 用户服务
type UserServiceClient interface {
	// 游客登录
	// 通过user_info.is_guest判断是否是游客
	GuestSignIn(ctx context.Context, in *GuestSignInReq, opts ...grpc.CallOption) (*GuestSignInRes, error)
	// 发送验证码（支持短信和 WhatsApp）
	// 适用于登录、注册、找回密码等场景。
	// 限制频率：一个手机号每 60 秒只能请求一次。
	// POST /api/user-account/user/v1/UserService/SendVerifyCode
	SendVerifyCode(ctx context.Context, in *SendVerifyCodeReq, opts ...grpc.CallOption) (*SendVerifyCodeRes, error)
	// 验证登录手机号
	// 适用于更换手机号场景。
	// 限制频率：一个手机号每 60 秒只能请求一次。
	// POST /api/user-account/user/v1/UserService/VerifyCode
	VerifyCode(ctx context.Context, in *VerifyCodeReq, opts ...grpc.CallOption) (*VerifyCodeRes, error)
	// 用户注册
	SignUp(ctx context.Context, in *SignUpReq, opts ...grpc.CallOption) (*SignUpRes, error)
	// 密码登录（邮箱，用户名）
	SignIn(ctx context.Context, in *SignInReq, opts ...grpc.CallOption) (*UserSignInRes, error)
	// 用户账号密码登录
	SignInByAccount(ctx context.Context, in *SignInByAccountReq, opts ...grpc.CallOption) (*SignInByAccountRes, error)
	// 手机号短信验证码登录(不存在时自动注册）
	// POST /api/user-account/user/v1/UserService/SignInByPhone
	SignInByPhone(ctx context.Context, in *SignInByPhoneReq, opts ...grpc.CallOption) (*SignInByPhoneRes, error)
	// 邮箱验证码登录(不存在时自动注册）
	// POST /api/user-account/user/v1/UserService/SignInByEmailReq
	SignInByEmail(ctx context.Context, in *SignInByEmailReq, opts ...grpc.CallOption) (*SignInByEmailRes, error)
	// 刷新token
	// POST /api/user-account/user/v1/UserService/RefreshToken
	RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenRes, error)
	// 获取用户信息
	// GET /api/user-account/user/v1/UserService/UserInfo
	UserInfo(ctx context.Context, in *UserInfoReq, opts ...grpc.CallOption) (*UserInfoRes, error)
	// 更新用户信息
	// POST /api/user-account/user/v1/UserService/UpdateUserInfo
	UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoRes, error)
	// 获取默认头像列表
	// GET /api/user-account/user/v1/UserService/AvatarList
	AvatarList(ctx context.Context, in *AvatarListReq, opts ...grpc.CallOption) (*AvatarListRes, error)
	// 手机号检查（判断是否为虚拟号/黑名单）
	PhoneValidCheck(ctx context.Context, in *PhoneValidCheckReq, opts ...grpc.CallOption) (*PhoneValidCheckRes, error)
	// 更换手机号码
	// POST /api/user-account/user/v1/UserService/ChangePhone
	ChangePhone(ctx context.Context, in *ChangePhoneReq, opts ...grpc.CallOption) (*ChangePhoneRes, error)
	// 邮箱已注册检查
	EmailValidCheck(ctx context.Context, in *EmailValidCheckReq, opts ...grpc.CallOption) (*EmailValidCheckRes, error)
	// 更换邮箱
	// POST /api/user-account/user/v1/UserService/ChangeEmail
	ChangeEmail(ctx context.Context, in *ChangeEmailReq, opts ...grpc.CallOption) (*ChangeEmailRes, error)
	LoginGoogle(ctx context.Context, in *LoginGoogleReq, opts ...grpc.CallOption) (*LoginGoogleRes, error)
	// 首次设置密码（或从无密码态补设）
	SetPassword(ctx context.Context, in *SetPasswordReq, opts ...grpc.CallOption) (*SetPasswordRes, error)
	// 已登录用户用旧密码改新密码
	ChangePassword(ctx context.Context, in *ChangePasswordReq, opts ...grpc.CallOption) (*ChangePasswordRes, error)
	// 两步式重置：用 reset_token 设置新密码
	ResetPassword(ctx context.Context, in *ResetPasswordReq, opts ...grpc.CallOption) (*ResetPasswordRes, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) GuestSignIn(ctx context.Context, in *GuestSignInReq, opts ...grpc.CallOption) (*GuestSignInRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GuestSignInRes)
	err := c.cc.Invoke(ctx, UserService_GuestSignIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SendVerifyCode(ctx context.Context, in *SendVerifyCodeReq, opts ...grpc.CallOption) (*SendVerifyCodeRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendVerifyCodeRes)
	err := c.cc.Invoke(ctx, UserService_SendVerifyCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) VerifyCode(ctx context.Context, in *VerifyCodeReq, opts ...grpc.CallOption) (*VerifyCodeRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyCodeRes)
	err := c.cc.Invoke(ctx, UserService_VerifyCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SignUp(ctx context.Context, in *SignUpReq, opts ...grpc.CallOption) (*SignUpRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignUpRes)
	err := c.cc.Invoke(ctx, UserService_SignUp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SignIn(ctx context.Context, in *SignInReq, opts ...grpc.CallOption) (*UserSignInRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserSignInRes)
	err := c.cc.Invoke(ctx, UserService_SignIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SignInByAccount(ctx context.Context, in *SignInByAccountReq, opts ...grpc.CallOption) (*SignInByAccountRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignInByAccountRes)
	err := c.cc.Invoke(ctx, UserService_SignInByAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SignInByPhone(ctx context.Context, in *SignInByPhoneReq, opts ...grpc.CallOption) (*SignInByPhoneRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignInByPhoneRes)
	err := c.cc.Invoke(ctx, UserService_SignInByPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SignInByEmail(ctx context.Context, in *SignInByEmailReq, opts ...grpc.CallOption) (*SignInByEmailRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignInByEmailRes)
	err := c.cc.Invoke(ctx, UserService_SignInByEmail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefreshTokenRes)
	err := c.cc.Invoke(ctx, UserService_RefreshToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UserInfo(ctx context.Context, in *UserInfoReq, opts ...grpc.CallOption) (*UserInfoRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserInfoRes)
	err := c.cc.Invoke(ctx, UserService_UserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateUserInfoRes)
	err := c.cc.Invoke(ctx, UserService_UpdateUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) AvatarList(ctx context.Context, in *AvatarListReq, opts ...grpc.CallOption) (*AvatarListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AvatarListRes)
	err := c.cc.Invoke(ctx, UserService_AvatarList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) PhoneValidCheck(ctx context.Context, in *PhoneValidCheckReq, opts ...grpc.CallOption) (*PhoneValidCheckRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PhoneValidCheckRes)
	err := c.cc.Invoke(ctx, UserService_PhoneValidCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ChangePhone(ctx context.Context, in *ChangePhoneReq, opts ...grpc.CallOption) (*ChangePhoneRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangePhoneRes)
	err := c.cc.Invoke(ctx, UserService_ChangePhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) EmailValidCheck(ctx context.Context, in *EmailValidCheckReq, opts ...grpc.CallOption) (*EmailValidCheckRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmailValidCheckRes)
	err := c.cc.Invoke(ctx, UserService_EmailValidCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ChangeEmail(ctx context.Context, in *ChangeEmailReq, opts ...grpc.CallOption) (*ChangeEmailRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangeEmailRes)
	err := c.cc.Invoke(ctx, UserService_ChangeEmail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) LoginGoogle(ctx context.Context, in *LoginGoogleReq, opts ...grpc.CallOption) (*LoginGoogleRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoginGoogleRes)
	err := c.cc.Invoke(ctx, UserService_LoginGoogle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SetPassword(ctx context.Context, in *SetPasswordReq, opts ...grpc.CallOption) (*SetPasswordRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetPasswordRes)
	err := c.cc.Invoke(ctx, UserService_SetPassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ChangePassword(ctx context.Context, in *ChangePasswordReq, opts ...grpc.CallOption) (*ChangePasswordRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangePasswordRes)
	err := c.cc.Invoke(ctx, UserService_ChangePassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ResetPassword(ctx context.Context, in *ResetPasswordReq, opts ...grpc.CallOption) (*ResetPasswordRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResetPasswordRes)
	err := c.cc.Invoke(ctx, UserService_ResetPassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations must embed UnimplementedUserServiceServer
// for forward compatibility.
//
// 用户服务
type UserServiceServer interface {
	// 游客登录
	// 通过user_info.is_guest判断是否是游客
	GuestSignIn(context.Context, *GuestSignInReq) (*GuestSignInRes, error)
	// 发送验证码（支持短信和 WhatsApp）
	// 适用于登录、注册、找回密码等场景。
	// 限制频率：一个手机号每 60 秒只能请求一次。
	// POST /api/user-account/user/v1/UserService/SendVerifyCode
	SendVerifyCode(context.Context, *SendVerifyCodeReq) (*SendVerifyCodeRes, error)
	// 验证登录手机号
	// 适用于更换手机号场景。
	// 限制频率：一个手机号每 60 秒只能请求一次。
	// POST /api/user-account/user/v1/UserService/VerifyCode
	VerifyCode(context.Context, *VerifyCodeReq) (*VerifyCodeRes, error)
	// 用户注册
	SignUp(context.Context, *SignUpReq) (*SignUpRes, error)
	// 密码登录（邮箱，用户名）
	SignIn(context.Context, *SignInReq) (*UserSignInRes, error)
	// 用户账号密码登录
	SignInByAccount(context.Context, *SignInByAccountReq) (*SignInByAccountRes, error)
	// 手机号短信验证码登录(不存在时自动注册）
	// POST /api/user-account/user/v1/UserService/SignInByPhone
	SignInByPhone(context.Context, *SignInByPhoneReq) (*SignInByPhoneRes, error)
	// 邮箱验证码登录(不存在时自动注册）
	// POST /api/user-account/user/v1/UserService/SignInByEmailReq
	SignInByEmail(context.Context, *SignInByEmailReq) (*SignInByEmailRes, error)
	// 刷新token
	// POST /api/user-account/user/v1/UserService/RefreshToken
	RefreshToken(context.Context, *RefreshTokenReq) (*RefreshTokenRes, error)
	// 获取用户信息
	// GET /api/user-account/user/v1/UserService/UserInfo
	UserInfo(context.Context, *UserInfoReq) (*UserInfoRes, error)
	// 更新用户信息
	// POST /api/user-account/user/v1/UserService/UpdateUserInfo
	UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoRes, error)
	// 获取默认头像列表
	// GET /api/user-account/user/v1/UserService/AvatarList
	AvatarList(context.Context, *AvatarListReq) (*AvatarListRes, error)
	// 手机号检查（判断是否为虚拟号/黑名单）
	PhoneValidCheck(context.Context, *PhoneValidCheckReq) (*PhoneValidCheckRes, error)
	// 更换手机号码
	// POST /api/user-account/user/v1/UserService/ChangePhone
	ChangePhone(context.Context, *ChangePhoneReq) (*ChangePhoneRes, error)
	// 邮箱已注册检查
	EmailValidCheck(context.Context, *EmailValidCheckReq) (*EmailValidCheckRes, error)
	// 更换邮箱
	// POST /api/user-account/user/v1/UserService/ChangeEmail
	ChangeEmail(context.Context, *ChangeEmailReq) (*ChangeEmailRes, error)
	LoginGoogle(context.Context, *LoginGoogleReq) (*LoginGoogleRes, error)
	// 首次设置密码（或从无密码态补设）
	SetPassword(context.Context, *SetPasswordReq) (*SetPasswordRes, error)
	// 已登录用户用旧密码改新密码
	ChangePassword(context.Context, *ChangePasswordReq) (*ChangePasswordRes, error)
	// 两步式重置：用 reset_token 设置新密码
	ResetPassword(context.Context, *ResetPasswordReq) (*ResetPasswordRes, error)
	mustEmbedUnimplementedUserServiceServer()
}

// UnimplementedUserServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserServiceServer struct{}

func (UnimplementedUserServiceServer) GuestSignIn(context.Context, *GuestSignInReq) (*GuestSignInRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GuestSignIn not implemented")
}
func (UnimplementedUserServiceServer) SendVerifyCode(context.Context, *SendVerifyCodeReq) (*SendVerifyCodeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendVerifyCode not implemented")
}
func (UnimplementedUserServiceServer) VerifyCode(context.Context, *VerifyCodeReq) (*VerifyCodeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyCode not implemented")
}
func (UnimplementedUserServiceServer) SignUp(context.Context, *SignUpReq) (*SignUpRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignUp not implemented")
}
func (UnimplementedUserServiceServer) SignIn(context.Context, *SignInReq) (*UserSignInRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignIn not implemented")
}
func (UnimplementedUserServiceServer) SignInByAccount(context.Context, *SignInByAccountReq) (*SignInByAccountRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignInByAccount not implemented")
}
func (UnimplementedUserServiceServer) SignInByPhone(context.Context, *SignInByPhoneReq) (*SignInByPhoneRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignInByPhone not implemented")
}
func (UnimplementedUserServiceServer) SignInByEmail(context.Context, *SignInByEmailReq) (*SignInByEmailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignInByEmail not implemented")
}
func (UnimplementedUserServiceServer) RefreshToken(context.Context, *RefreshTokenReq) (*RefreshTokenRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedUserServiceServer) UserInfo(context.Context, *UserInfoReq) (*UserInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfo not implemented")
}
func (UnimplementedUserServiceServer) UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfo not implemented")
}
func (UnimplementedUserServiceServer) AvatarList(context.Context, *AvatarListReq) (*AvatarListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AvatarList not implemented")
}
func (UnimplementedUserServiceServer) PhoneValidCheck(context.Context, *PhoneValidCheckReq) (*PhoneValidCheckRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PhoneValidCheck not implemented")
}
func (UnimplementedUserServiceServer) ChangePhone(context.Context, *ChangePhoneReq) (*ChangePhoneRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangePhone not implemented")
}
func (UnimplementedUserServiceServer) EmailValidCheck(context.Context, *EmailValidCheckReq) (*EmailValidCheckRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EmailValidCheck not implemented")
}
func (UnimplementedUserServiceServer) ChangeEmail(context.Context, *ChangeEmailReq) (*ChangeEmailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeEmail not implemented")
}
func (UnimplementedUserServiceServer) LoginGoogle(context.Context, *LoginGoogleReq) (*LoginGoogleRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoginGoogle not implemented")
}
func (UnimplementedUserServiceServer) SetPassword(context.Context, *SetPasswordReq) (*SetPasswordRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPassword not implemented")
}
func (UnimplementedUserServiceServer) ChangePassword(context.Context, *ChangePasswordReq) (*ChangePasswordRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangePassword not implemented")
}
func (UnimplementedUserServiceServer) ResetPassword(context.Context, *ResetPasswordReq) (*ResetPasswordRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetPassword not implemented")
}
func (UnimplementedUserServiceServer) mustEmbedUnimplementedUserServiceServer() {}
func (UnimplementedUserServiceServer) testEmbeddedByValue()                     {}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s grpc.ServiceRegistrar, srv UserServiceServer) {
	// If the following call pancis, it indicates UnimplementedUserServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserService_ServiceDesc, srv)
}

func _UserService_GuestSignIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuestSignInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GuestSignIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GuestSignIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GuestSignIn(ctx, req.(*GuestSignInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SendVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SendVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SendVerifyCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SendVerifyCode(ctx, req.(*SendVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_VerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).VerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_VerifyCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).VerifyCode(ctx, req.(*VerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SignUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SignUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SignUp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SignUp(ctx, req.(*SignUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SignIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SignIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SignIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SignIn(ctx, req.(*SignInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SignInByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SignInByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SignInByAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SignInByAccount(ctx, req.(*SignInByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SignInByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInByPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SignInByPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SignInByPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SignInByPhone(ctx, req.(*SignInByPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SignInByEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInByEmailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SignInByEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SignInByEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SignInByEmail(ctx, req.(*SignInByEmailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RefreshToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RefreshToken(ctx, req.(*RefreshTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UserInfo(ctx, req.(*UserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUserInfo(ctx, req.(*UpdateUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_AvatarList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AvatarListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).AvatarList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_AvatarList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).AvatarList(ctx, req.(*AvatarListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_PhoneValidCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PhoneValidCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).PhoneValidCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_PhoneValidCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).PhoneValidCheck(ctx, req.(*PhoneValidCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ChangePhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangePhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ChangePhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ChangePhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ChangePhone(ctx, req.(*ChangePhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_EmailValidCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmailValidCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).EmailValidCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_EmailValidCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).EmailValidCheck(ctx, req.(*EmailValidCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ChangeEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeEmailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ChangeEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ChangeEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ChangeEmail(ctx, req.(*ChangeEmailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_LoginGoogle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginGoogleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).LoginGoogle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_LoginGoogle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).LoginGoogle(ctx, req.(*LoginGoogleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SetPassword(ctx, req.(*SetPasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ChangePassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangePasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ChangePassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ChangePassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ChangePassword(ctx, req.(*ChangePasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ResetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetPasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ResetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ResetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ResetPassword(ctx, req.(*ResetPasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserService_ServiceDesc is the grpc.ServiceDesc for UserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "user.v1.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GuestSignIn",
			Handler:    _UserService_GuestSignIn_Handler,
		},
		{
			MethodName: "SendVerifyCode",
			Handler:    _UserService_SendVerifyCode_Handler,
		},
		{
			MethodName: "VerifyCode",
			Handler:    _UserService_VerifyCode_Handler,
		},
		{
			MethodName: "SignUp",
			Handler:    _UserService_SignUp_Handler,
		},
		{
			MethodName: "SignIn",
			Handler:    _UserService_SignIn_Handler,
		},
		{
			MethodName: "SignInByAccount",
			Handler:    _UserService_SignInByAccount_Handler,
		},
		{
			MethodName: "SignInByPhone",
			Handler:    _UserService_SignInByPhone_Handler,
		},
		{
			MethodName: "SignInByEmail",
			Handler:    _UserService_SignInByEmail_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _UserService_RefreshToken_Handler,
		},
		{
			MethodName: "UserInfo",
			Handler:    _UserService_UserInfo_Handler,
		},
		{
			MethodName: "UpdateUserInfo",
			Handler:    _UserService_UpdateUserInfo_Handler,
		},
		{
			MethodName: "AvatarList",
			Handler:    _UserService_AvatarList_Handler,
		},
		{
			MethodName: "PhoneValidCheck",
			Handler:    _UserService_PhoneValidCheck_Handler,
		},
		{
			MethodName: "ChangePhone",
			Handler:    _UserService_ChangePhone_Handler,
		},
		{
			MethodName: "EmailValidCheck",
			Handler:    _UserService_EmailValidCheck_Handler,
		},
		{
			MethodName: "ChangeEmail",
			Handler:    _UserService_ChangeEmail_Handler,
		},
		{
			MethodName: "LoginGoogle",
			Handler:    _UserService_LoginGoogle_Handler,
		},
		{
			MethodName: "SetPassword",
			Handler:    _UserService_SetPassword_Handler,
		},
		{
			MethodName: "ChangePassword",
			Handler:    _UserService_ChangePassword_Handler,
		},
		{
			MethodName: "ResetPassword",
			Handler:    _UserService_ResetPassword_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user/v1/user.proto",
}
