// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CalendarEventConfig is the golang structure of table calendar_event_config for DAO operations like Where/Data.
type CalendarEventConfig struct {
	g.Meta        `orm:"table:calendar_event_config, do:true"`
	Id            interface{} //
	Title         interface{} // 事件标题
	EventType     interface{} // 事件类型
	EventCategory interface{} // 事件分类
	Year          interface{} // 年份
	Month         interface{} // 月份
	Day           interface{} // 日期
	Weekday       interface{} // 星期几
	Route         interface{} // 路由路径
	Params        interface{} // 参数对象
	CreateTime    interface{} // 创建时间
}
