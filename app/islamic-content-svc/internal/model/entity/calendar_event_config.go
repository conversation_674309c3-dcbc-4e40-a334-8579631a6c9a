// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// CalendarEventConfig is the golang structure for table calendar_event_config.
type CalendarEventConfig struct {
	Id            int    `json:"id"            orm:"id"             description:""`     //
	Title         string `json:"title"         orm:"title"          description:"事件标题"` // 事件标题
	EventType     string `json:"eventType"     orm:"event_type"     description:"事件类型"` // 事件类型
	EventCategory string `json:"eventCategory" orm:"event_category" description:"事件分类"` // 事件分类
	Year          int    `json:"year"          orm:"year"           description:"年份"`   // 年份
	Month         int    `json:"month"         orm:"month"          description:"月份"`   // 月份
	Day           int    `json:"day"           orm:"day"            description:"日期"`   // 日期
	Weekday       int    `json:"weekday"       orm:"weekday"        description:"星期几"`  // 星期几
	Route         string `json:"route"         orm:"route"          description:"路由路径"` // 路由路径
	Params        string `json:"params"        orm:"params"         description:"参数对象"` // 参数对象
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"` // 创建时间
}
