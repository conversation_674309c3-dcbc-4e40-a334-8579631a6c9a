package errno

import "github.com/gogf/gf/v2/errors/gcode"

var (
	CodeRecodeNotExist = gcode.New(3001, "error.record.not.exist", nil) // 记录找不到

	// 视频相关错误码
	CodeVideoNotFound        = gcode.New(3101, "error.video.not.found", nil)        // 视频不存在
	CodeVideoNotPublished    = gcode.New(3102, "error.video.not.published", nil)    // 视频未发布
	CodePlaylistNotFound     = gcode.New(3103, "error.playlist.not.found", nil)     // 播放列表不存在
	CodeVideoCollectFailed   = gcode.New(3104, "error.video.collect.failed", nil)   // 视频收藏失败
	CodeVideoUncollectFailed = gcode.New(3105, "error.video.uncollect.failed", nil) // 视频取消收藏失败
	CodeVideoShareFailed     = gcode.New(3106, "error.video.share.failed", nil)     // 视频分享失败
)
