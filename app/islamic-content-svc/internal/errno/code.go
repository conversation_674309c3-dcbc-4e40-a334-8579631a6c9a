package errno

import "github.com/gogf/gf/v2/errors/gcode"

// api返回错误码
// 错误定义gcode，多语言error： errno.T(ctx, errno.CodeInvalidParams)
// controller返回错误示例： return nil, errno.T(ctx, errno.CodeInvalidParams)
// 多语言文件在 manifest/i18n 随程序一起打包
var (
	CodeSuccess = gcode.New(200, "success", nil) // 成功

	// 1xxx 通用（未知）
	CodeInternalError   = gcode.New(1000, "common.internal.error", nil)    // 内部错误
	CodeInvalidParams   = gcode.New(1001, "common.invalid.params", nil)    // 参数错误
	CodeNotFound        = gcode.New(1002, "common.not.found", nil)         // 资源不存在
	CodeUnauthorized    = gcode.New(1003, "common.unauthorized", nil)      // 未授权
	CodeForbidden       = gcode.New(1004, "common.forbidden", nil)         // 禁止访问
	CodeTooManyRequests = gcode.New(1005, "common.too.many.requests", nil) // 请求过多
	CodeExpired         = gcode.New(1006, "common.expired", nil)           // 信息过期
	// 2xxxx user-account-svc 用户微服务相关
	// 3xxxx islamic-content-svc 古兰经cms微服务
	// 5xxxx file-storage-svc 文件微服务
	// 6xxxx notify-svc 消息推送微服务
	// 7xxxx wealth-charity-svc
	// 8xxxx mall-svc
	// 9xxxx payment-svc
)
