// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// calendarEventConfigDao is the data access object for the table calendar_event_config.
// You can define custom methods on it to extend its functionality as needed.
type calendarEventConfigDao struct {
	*internal.CalendarEventConfigDao
}

var (
	// CalendarEventConfig is a globally accessible object for table calendar_event_config operations.
	CalendarEventConfig = calendarEventConfigDao{internal.NewCalendarEventConfigDao()}
)

// Add your custom methods and functionality below.
