// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CalendarEventConfigDao is the data access object for the table calendar_event_config.
type CalendarEventConfigDao struct {
	table    string                     // table is the underlying table name of the DAO.
	group    string                     // group is the database configuration group name of the current DAO.
	columns  CalendarEventConfigColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler         // handlers for customized model modification.
}

// CalendarEventConfigColumns defines and stores column names for the table calendar_event_config.
type CalendarEventConfigColumns struct {
	Id            string //
	Title         string // 事件标题
	EventType     string // 事件类型
	EventCategory string // 事件分类
	Year          string // 年份
	Month         string // 月份
	Day           string // 日期
	Weekday       string // 星期几
	Route         string // 路由路径
	Params        string // 参数对象
	CreateTime    string // 创建时间
}

// calendarEventConfigColumns holds the columns for the table calendar_event_config.
var calendarEventConfigColumns = CalendarEventConfigColumns{
	Id:            "id",
	Title:         "title",
	EventType:     "event_type",
	EventCategory: "event_category",
	Year:          "year",
	Month:         "month",
	Day:           "day",
	Weekday:       "weekday",
	Route:         "route",
	Params:        "params",
	CreateTime:    "create_time",
}

// NewCalendarEventConfigDao creates and returns a new DAO object for table data access.
func NewCalendarEventConfigDao(handlers ...gdb.ModelHandler) *CalendarEventConfigDao {
	return &CalendarEventConfigDao{
		group:    "default",
		table:    "calendar_event_config",
		columns:  calendarEventConfigColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CalendarEventConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CalendarEventConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CalendarEventConfigDao) Columns() CalendarEventConfigColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CalendarEventConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CalendarEventConfigDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CalendarEventConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
