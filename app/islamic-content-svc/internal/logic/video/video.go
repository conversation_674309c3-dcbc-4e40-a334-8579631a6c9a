package video

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"

	"halalplus/app/islamic-content-svc/internal/consts"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/errno"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"
)

type sVideo struct{}

func init() {
	service.RegisterVideo(New())
}

func New() service.IVideo {
	return &sVideo{}
}

// PlaylistList 获取视频播放列表
func (s *sVideo) PlaylistList(ctx context.Context, languageId uint32, page int, size int) (*model.VideoPlaylistListOutput, error) {
	query := dao.VideoPlaylists.Ctx(ctx).
		Where(dao.VideoPlaylists.Columns().IsVisible, 1).
		Where(dao.VideoPlaylists.Columns().DeleteTime, 0)

	total, err := query.Count()
	if err != nil {
		g.Log().Error(ctx, "查询播放列表总数失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	var playlists []*entity.VideoPlaylists
	err = query.Page(page, size).Order(dao.VideoPlaylists.Columns().SortOrder, "ASC").Scan(&playlists)
	if err != nil {
		g.Log().Error(ctx, "查询播放列表失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	// 获取播放列表ID列表
	playlistIds := make([]uint, 0, len(playlists))
	for _, playlist := range playlists {
		playlistIds = append(playlistIds, playlist.Id)
	}

	// 查询多语言信息
	var playlistLanguages []*entity.VideoPlaylistLanguages
	if len(playlistIds) > 0 {
		err = dao.VideoPlaylistLanguages.Ctx(ctx).
			WhereIn(dao.VideoPlaylistLanguages.Columns().PlaylistId, playlistIds).
			Where(dao.VideoPlaylistLanguages.Columns().LanguageId, languageId).
			Where(dao.VideoPlaylistLanguages.Columns().DeleteTime, 0).
			Scan(&playlistLanguages)
		if err != nil {
			g.Log().Error(ctx, "查询播放列表多语言信息失败:", err)
			return nil, errno.Te(ctx, errno.CodeInternalError, err)
		}
	}

	languageMap := make(map[uint]*entity.VideoPlaylistLanguages)
	for _, lang := range playlistLanguages {
		languageMap[lang.PlaylistId] = lang
	}

	var playlistInfos []*model.VideoPlaylistInfo
	for _, playlist := range playlists {
		playlistInfo := &model.VideoPlaylistInfo{
			PlaylistId: uint32(playlist.Id),
			CoverUrl:   playlist.CoverUrl,
			VideoCount: uint32(playlist.VideoCount),
		}

		// 设置多语言信息
		if lang, exists := languageMap[playlist.Id]; exists {
			playlistInfo.Name = lang.Name
			playlistInfo.ShortTitle = lang.ShortTitle
			playlistInfo.Description = lang.Description
		}

		playlistInfos = append(playlistInfos, playlistInfo)
	}

	return &model.VideoPlaylistListOutput{
		List:  playlistInfos,
		Total: total,
	}, nil
}

// VideoList 获取视频列表（支持分类、播放列表、搜索）
func (s *sVideo) VideoList(ctx context.Context, input *model.VideoListInput) (*model.VideoListOutput, error) {
	// 构建基础查询条件
	baseQuery := dao.Videos.Ctx(ctx).As("v").
		LeftJoin(dao.VideoLanguages.Table()+" vl", "vl.video_id = v.id").
		Where("v.publish_state", consts.VideoPublishStatePublished). // 只查询已发布的视频
		Where("v.delete_time", 0).
		Where("vl.language_id", input.LanguageId).
		Where("vl.delete_time", 0)

	// 分类筛选
	if input.CategoryId > 0 {
		baseQuery = baseQuery.Where("v.category_id", input.CategoryId)
	}

	// playlist筛选
	var playlistInfo *model.PlaylistBasicInfo
	if input.PlaylistId > 0 {
		baseQuery = baseQuery.InnerJoin(dao.VideoPlaylistRelations.Table()+" vpr", "vpr.video_id = v.id").
			Where("vpr.playlist_id", input.PlaylistId)

		// 获取播放列表基本信息
		playlistInfo, _ = s.getPlaylistBasicInfo(ctx, input.PlaylistId, input.LanguageId)
	}

	// 标题搜索
	if input.Title != "" {
		baseQuery = baseQuery.WhereLike("vl.title", "%"+input.Title+"%") // TODO: 暂时只有模糊搜索
	}

	// 计算总数
	total, err := baseQuery.Count()
	if err != nil {
		g.Log().Error(ctx, "查询视频总数失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	// 构建数据查询
	dataQuery := baseQuery.Fields("v.*, vl.title, vl.description")
	if input.PlaylistId > 0 {
		dataQuery = dataQuery.Order("vpr.sort_order ASC")
	} else {
		dataQuery = dataQuery.Order("v.publish_time DESC")
	}

	var videos []struct {
		*entity.Videos
		Title       string `json:"title"`
		Description string `json:"description"`
	}
	err = dataQuery.Page(input.Page, input.Size).Scan(&videos)
	if err != nil {
		g.Log().Error(ctx, "查询视频列表失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	// 获取分类ID列表
	categoryIds := make([]uint, 0, len(videos))
	for _, video := range videos {
		categoryIds = append(categoryIds, video.CategoryId)
	}

	// 获取分类名称映射
	categoryNameMap, err := s.getCategoryNameMap(ctx, categoryIds, input.LanguageId)
	if err != nil {
		return nil, err
	}

	// 转换
	var videoItems []*model.VideoListItem
	for _, video := range videos {
		videoItem := &model.VideoListItem{
			VideoId:       uint32(video.Id),
			CategoryId:    uint32(video.CategoryId),
			Title:         video.Title,
			VideoCoverUrl: video.VideoCoverUrl,
			VideoDuration: uint32(video.VideoDuration),
			CategoryName:  categoryNameMap[video.CategoryId],
		}
		videoItems = append(videoItems, videoItem)
	}

	return &model.VideoListOutput{
		List:     videoItems,
		Playlist: playlistInfo,
		Total:    total,
	}, nil
}

// getCategoryNameMap 根据分类ID列表和语言ID获取分类名称映射
func (s *sVideo) getCategoryNameMap(ctx context.Context, categoryIds []uint, languageId uint32) (map[uint]string, error) {
	if len(categoryIds) == 0 {
		return make(map[uint]string), nil
	}

	var categoryLanguages []*entity.VideoCategoryLanguages
	err := dao.VideoCategoryLanguages.Ctx(ctx).
		WhereIn(dao.VideoCategoryLanguages.Columns().CategoryId, categoryIds).
		Where(dao.VideoCategoryLanguages.Columns().LanguageId, languageId).
		Where(dao.VideoCategoryLanguages.Columns().DeleteTime, 0).
		Scan(&categoryLanguages)
	if err != nil {
		g.Log().Error(ctx, "查询分类多语言信息失败:", err)
		return nil, errno.Te(ctx, errno.CodeCategoryNotFound, err)
	}

	categoryNameMap := make(map[uint]string)
	for _, categoryLang := range categoryLanguages {
		categoryNameMap[categoryLang.CategoryId] = categoryLang.Name
	}

	return categoryNameMap, nil
}

// VideoDetail 获取视频详情
func (s *sVideo) VideoDetail(ctx context.Context, videoId uint32, languageId uint32, userId uint64) (*model.VideoDetailOutput, error) {

	var video *entity.Videos
	err := dao.Videos.Ctx(ctx).
		Where(dao.Videos.Columns().Id, videoId).
		Where(dao.Videos.Columns().PublishState, consts.VideoPublishStatePublished).
		Where(dao.Videos.Columns().DeleteTime, 0).
		Scan(&video)
	if err != nil {
		g.Log().Error(ctx, "查询视频信息失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}
	if video == nil {
		g.Log().Warning(ctx, "视频不存在或未发布, videoId:", videoId)
		return nil, errno.T(ctx, errno.CodeVideoNotFound)
	}

	// 查询多语言
	var videoLang *entity.VideoLanguages
	err = dao.VideoLanguages.Ctx(ctx).
		Where(dao.VideoLanguages.Columns().VideoId, videoId).
		Where(dao.VideoLanguages.Columns().LanguageId, languageId).
		Where(dao.VideoLanguages.Columns().DeleteTime, 0).
		Scan(&videoLang)
	if err != nil {
		g.Log().Error(ctx, "查询视频多语言信息失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	// 检查用户是否收藏
	isCollected := false
	if userId > 0 {
		count, err := dao.VideoCollects.Ctx(ctx).
			Where(dao.VideoCollects.Columns().UserId, userId).
			Where(dao.VideoCollects.Columns().VideoId, videoId).
			Count()
		if err == nil && count > 0 {
			isCollected = true
		}
	}

	// 获取分类名称映射
	categoryNameMap, err := s.getCategoryNameMap(ctx, []uint{video.CategoryId}, languageId)
	if err != nil {
		return nil, err
	}

	videoDetail := &model.VideoDetailInfo{
		VideoId:          uint32(video.Id),
		CategoryId:       uint32(video.CategoryId),
		VideoCoverUrl:    video.VideoCoverUrl,
		VideoUrl:         video.VideoUrl,
		Author:           video.Author,
		AuthorLogo:       video.AuthorLogo,
		AuthorAuthStatus: uint32(video.AuthorAuthStatus),
		PublishState:     uint32(video.PublishState),
		IsCollected:      isCollected,
		CategoryName:     categoryNameMap[video.CategoryId],
		PublishTime:      int64(video.PublishTime),
	}

	// 设置多语言信息
	if videoLang != nil {
		videoDetail.Title = videoLang.Title
		videoDetail.Description = videoLang.Description
	}

	return &model.VideoDetailOutput{
		Video: videoDetail,
	}, nil
}

// RecommendedVideoList 获取推荐视频列表
func (s *sVideo) RecommendedVideoList(ctx context.Context, input *model.RecommendedVideoListInput) (*model.VideoListOutput, error) {

	// 构建基础查询条件
	baseQuery := dao.Videos.Ctx(ctx).As("v").
		LeftJoin(dao.VideoLanguages.Table()+" vl", "vl.video_id = v.id").
		Where("v.publish_state", consts.VideoPublishStatePublished).
		Where("v.is_recommended", 1). // 只查询推荐视频
		Where("vl.language_id", input.LanguageId).
		Where("v.delete_time", 0).
		Where("vl.delete_time", 0)

	// 分类筛选
	if input.CategoryId > 0 {
		baseQuery = baseQuery.Where("v.category_id", input.CategoryId)
	}

	// 计算总数
	total, err := baseQuery.Count()
	if err != nil {
		g.Log().Error(ctx, "查询推荐视频总数失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	// 构建数据查询
	dataQuery := baseQuery.Fields("v.*, vl.title, vl.description").Order("v.publish_time DESC")

	var videos []struct {
		*entity.Videos
		Title       string `json:"title"`
		Description string `json:"description"`
	}
	err = dataQuery.Page(input.Page, input.Size).Scan(&videos)
	if err != nil {
		g.Log().Error(ctx, "查询推荐视频列表失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	// 获取分类ID列表
	categoryIds := make([]uint, 0, len(videos))
	for _, video := range videos {
		categoryIds = append(categoryIds, video.CategoryId)
	}

	// 获取分类名称映射
	categoryNameMap, err := s.getCategoryNameMap(ctx, categoryIds, input.LanguageId)
	if err != nil {
		return nil, err
	}

	// 转换
	var videoItems []*model.VideoListItem
	for _, video := range videos {
		videoItem := &model.VideoListItem{
			VideoId:       uint32(video.Id),
			CategoryId:    uint32(video.CategoryId),
			Title:         video.Title,
			VideoCoverUrl: video.VideoCoverUrl,
			VideoDuration: uint32(video.VideoDuration),
			CategoryName:  categoryNameMap[video.CategoryId],
		}
		videoItems = append(videoItems, videoItem)
	}

	return &model.VideoListOutput{
		List:  videoItems,
		Total: total,
	}, nil
}

// VideoCollect 视频收藏/取消收藏
func (s *sVideo) VideoCollect(ctx context.Context, input *model.VideoCollectInput) error {

	if input.IsAdd == 1 {
		// 添加收藏
		result, err := dao.VideoCollects.Ctx(ctx).
			InsertIgnore(g.Map{
				dao.VideoCollects.Columns().UserId:     input.UserId,
				dao.VideoCollects.Columns().VideoId:    input.VideoId,
				dao.VideoCollects.Columns().CreateTime: time.Now().UnixMilli(),
			})
		if err != nil {
			g.Log().Error(ctx, "添加视频收藏失败:", err)
			return errno.Te(ctx, errno.CodeVideoCollectFailed, err)
		}

		// 只有真正插入了新记录才更新收藏数
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			g.Log().Error(ctx, "获取插入结果失败:", err)
			return errno.Te(ctx, errno.CodeVideoCollectFailed, err)
		}

		if rowsAffected > 0 {
			// 更新视频收藏数
			_, err = dao.Videos.Ctx(ctx).
				Where(dao.Videos.Columns().Id, input.VideoId).
				Increment(dao.Videos.Columns().CollectCount, 1)
			if err != nil {
				g.Log().Error(ctx, "更新视频收藏数失败:", err)
			}
		}
	} else {
		// 取消收藏
		result, err := dao.VideoCollects.Ctx(ctx).
			Where(dao.VideoCollects.Columns().UserId, input.UserId).
			Where(dao.VideoCollects.Columns().VideoId, input.VideoId).
			Delete()
		if err != nil {
			g.Log().Error(ctx, "取消视频收藏失败:", err)
			return errno.Te(ctx, errno.CodeVideoUncollectFailed, err)
		}

		// 只有真正删除了记录才更新收藏数
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			g.Log().Error(ctx, "获取删除结果失败:", err)
			return errno.Te(ctx, errno.CodeVideoUncollectFailed, err)
		}

		if rowsAffected > 0 {
			// 更新视频收藏数
			_, err = dao.Videos.Ctx(ctx).
				Where(dao.Videos.Columns().Id, input.VideoId).
				Where(dao.Videos.Columns().CollectCount+" > 0").
				Decrement(dao.Videos.Columns().CollectCount, 1)
			if err != nil {
				g.Log().Error(ctx, "更新视频收藏数失败:", err)
			}
		}
	}

	return nil
}

// VideoCollectList 获取用户收藏的视频列表
func (s *sVideo) VideoCollectList(ctx context.Context, userId uint64, languageId uint32, page, size int) (*model.VideoListOutput, error) {

	// 构建基础查询条件
	baseQuery := dao.VideoCollects.Ctx(ctx).As("vc").
		InnerJoin(dao.Videos.Table()+" v", "v.id = vc.video_id").
		LeftJoin(dao.VideoLanguages.Table()+" vl", "vl.video_id = v.id").
		Where("vc.user_id", userId).
		Where("v.publish_state", consts.VideoPublishStatePublished).
		Where("vl.language_id", languageId).
		Where("v.delete_time", 0).
		Where("vl.delete_time", 0)

	// 计算总数
	total, err := baseQuery.Count()
	if err != nil {
		g.Log().Error(ctx, "查询用户收藏视频总数失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	// 构建数据查询
	dataQuery := baseQuery.Fields("v.*, vl.title, vl.description, vc.create_time as collect_time").Order("vc.create_time DESC")

	// 分页查询
	var videos []struct {
		*entity.Videos
		Title       string `json:"title"`
		Description string `json:"description"`
	}
	err = dataQuery.Page(page, size).Scan(&videos)
	if err != nil {
		g.Log().Error(ctx, "查询用户收藏视频列表失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	// 获取分类ID列表
	categoryIds := make([]uint, 0, len(videos))
	for _, video := range videos {
		categoryIds = append(categoryIds, video.CategoryId)
	}

	// 获取分类名称映射
	categoryNameMap, err := s.getCategoryNameMap(ctx, categoryIds, languageId)
	if err != nil {
		return nil, err
	}

	var videoItems []*model.VideoListItem
	for _, video := range videos {
		videoItem := &model.VideoListItem{
			VideoId:       uint32(video.Id),
			CategoryId:    uint32(video.CategoryId),
			Title:         video.Title,
			VideoCoverUrl: video.VideoCoverUrl,
			VideoDuration: uint32(video.VideoDuration),
			CategoryName:  categoryNameMap[video.CategoryId],
		}
		videoItems = append(videoItems, videoItem)
	}

	return &model.VideoListOutput{
		List:  videoItems,
		Total: total,
	}, nil
}

// getPlaylistBasicInfo 获取播放列表基本信息
func (s *sVideo) getPlaylistBasicInfo(ctx context.Context, playlistId uint32, languageId uint32) (*model.PlaylistBasicInfo, error) {
	// 查询播放列表基本信息
	var playlist *entity.VideoPlaylists
	err := dao.VideoPlaylists.Ctx(ctx).
		Where(dao.VideoPlaylists.Columns().Id, playlistId).
		Where(dao.VideoPlaylists.Columns().IsVisible, 1).
		Scan(&playlist)
	if err != nil {
		g.Log().Error(ctx, "查询播放列表基本信息失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	if playlist == nil {
		g.Log().Warning(ctx, "播放列表不存在, playlistId:", playlistId)
		return nil, errno.T(ctx, errno.CodePlaylistNotFound)
	}

	// 查询播放列表多语言信息
	var playlistLang *entity.VideoPlaylistLanguages
	err = dao.VideoPlaylistLanguages.Ctx(ctx).
		Where(dao.VideoPlaylistLanguages.Columns().PlaylistId, playlistId).
		Where(dao.VideoPlaylistLanguages.Columns().LanguageId, languageId).
		Where(dao.VideoPlaylistLanguages.Columns().DeleteTime, 0).
		Scan(&playlistLang)
	if err != nil {
		g.Log().Error(ctx, "查询播放列表多语言信息失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	playlistInfo := &model.PlaylistBasicInfo{
		PlaylistId: playlistId,
		CoverUrl:   playlist.CoverUrl,
	}

	// 设置多语言信息
	if playlistLang != nil {
		playlistInfo.Name = playlistLang.Name
	}

	return playlistInfo, nil
}

// VideoCollectStatusCheck 视频收藏状态检查
func (s *sVideo) VideoCollectStatusCheck(ctx context.Context, userId uint64, videoId uint32) (*model.VideoCollectStatusCheckOutput, error) {

	count, err := dao.VideoCollects.Ctx(ctx).
		Where(dao.VideoCollects.Columns().UserId, userId).
		Where(dao.VideoCollects.Columns().VideoId, videoId).
		Count()
	if err != nil {
		g.Log().Error(ctx, "查询视频收藏状态失败:", err)
		return nil, errno.Te(ctx, errno.CodeInternalError, err)
	}

	return &model.VideoCollectStatusCheckOutput{
		IsCollect: count,
	}, nil
}

// VideoShare 视频分享
func (s *sVideo) VideoShare(ctx context.Context, input *model.VideoShareInput) error {
	// 记录分享记录到数据库
	_, err := dao.VideoShares.Ctx(ctx).Insert(&entity.VideoShares{
		UserId:     input.UserId,
		VideoId:    uint(input.VideoId),
		CreateTime: uint64(time.Now().UnixMilli()),
	})
	if err != nil {
		g.Log().Error(ctx, "记录视频分享失败:", err)
		return errno.Te(ctx, errno.CodeVideoShareFailed, err)
	}

	return nil
}
