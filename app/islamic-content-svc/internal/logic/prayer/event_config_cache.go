package prayer

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"sync"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcron"
)

const (
	EventTypeAnnualGregorian = "annualGregorian" // 每年公历固定日期
	EventTypeAnnualHijri     = "annualHijri"     // 每年伊斯兰历固定日期
	EventTypeMonthlyHijri    = "monthlyHijri"    // 每月伊斯兰历固定日期
	EventTypeOnce            = "once"            // 一次性事件
	EventTypeWeeklyGregorian = "weeklyGregorian" // 每周固定星期
)

type EventConfigCache struct {
	mu      sync.RWMutex
	configs []*entity.CalendarEventConfig // 所有配置
}

func (c *EventConfigCache) start() {
	// 初始加载配置
	c.loadEventConfigs()

	// 每小时刷新一次
	_, err := gcron.AddSingleton(context.Background(), "@hourly", func(ctx context.Context) {
		c.loadEventConfigs()
	})
	if err != nil {
		g.Log().Error(context.Background(), "start calendar event config cron job failed:", err)
	}
}

func (c *EventConfigCache) loadEventConfigs() {
	ctx := context.Background()

	var configs []*entity.CalendarEventConfig
	err := dao.CalendarEventConfig.Ctx(ctx).Scan(&configs)
	if err != nil {
		g.Log().Error(ctx, "加载事件配置失败:", err)
		return
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	c.configs = configs
}

// 根据日期获取事件
func (c *EventConfigCache) getEventsForDate(gregorianYear, gregorianMonth, gregorianDay int, hijriYear, hijriMonth, hijriDay int, weekday int) []*model.CalendarEventInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var events []*model.CalendarEventInfo

	// 检查是否为禁止斋戒的日子
	isFastingForbidden := c.isFastingForbiddenDate(hijriMonth, hijriDay)

	for _, config := range c.configs {
		// 如果是斋戒事件且在禁止斋戒的日子，则跳过
		if config.EventCategory == "fasting" && isFastingForbidden {
			continue
		}

		switch config.EventType {
		case EventTypeAnnualGregorian:
			if config.Month == gregorianMonth && config.Day == gregorianDay {
				events = append(events, c.convertToEventInfo(config))
			}
		case EventTypeAnnualHijri:
			if config.Month == hijriMonth && config.Day == hijriDay {
				events = append(events, c.convertToEventInfo(config))
			}
		case EventTypeMonthlyHijri:
			if config.Day == hijriDay {
				events = append(events, c.convertToEventInfo(config))
			}
		case EventTypeOnce:
			if config.Year == gregorianYear && config.Month == gregorianMonth && config.Day == gregorianDay {
				events = append(events, c.convertToEventInfo(config))
			}
		case EventTypeWeeklyGregorian:
			if config.Weekday == weekday {
				events = append(events, c.convertToEventInfo(config))
			}
		}
	}

	return events
}

// isFastingForbiddenDate 检查是否为禁止斋戒的日子
// 根据伊斯兰教法，以下日子禁止斋戒：
// 1. 开斋节（伊斯兰历10月1日）
// 2. 古尔邦节/宰牲节（伊斯兰历12月10日）
// 3. 塔什里克日（伊斯兰历12月11、12、13日）
func (c *EventConfigCache) isFastingForbiddenDate(hijriMonth, hijriDay int) bool {
	// 开斋节：伊斯兰历10月1日
	if hijriMonth == 10 && hijriDay == 1 {
		return true
	}

	// 古尔邦节/宰牲节：伊斯兰历12月10日
	if hijriMonth == 12 && hijriDay == 10 {
		return true
	}

	// 塔什里克日：伊斯兰历12月11、12、13日
	// 这三天是古尔邦节后的日子，也禁止斋戒
	if hijriMonth == 12 && (hijriDay == 11 || hijriDay == 12 || hijriDay == 13) {
		return true
	}

	return false
}

// 转换一下 CalendarEventConfig 为 CalendarEventInfo
func (c *EventConfigCache) convertToEventInfo(config *entity.CalendarEventConfig) *model.CalendarEventInfo {
	var eventType string
	switch config.EventCategory {
	case "holiday":
		eventType = "HARI_BESAR" // 前端会把这个标红点
	case "fasting":
		eventType = "PUASA"
	default:
		eventType = "LIBUR_NASIONAL"
	}

	return &model.CalendarEventInfo{
		Id:          int64(config.Id),
		EventType:   eventType,
		Title:       config.Title,
		Description: "", // 配置表中没有description字段，如果需要可以扩展
		JumpUrl:     "", // 暂时不支持
	}
}
