"accept-language" = "english"
"success" = "success"
"error.record.not.exist" = "Record not found"

# Common error messages
"common.internal.error" = "Internal error"
"common.invalid.params" = "Invalid parameters"
"common.not.found" = "Resource not found"
"common.unauthorized" = "Unauthorized"
"common.forbidden" = "Forbidden"
"common.too.many.requests" = "Too many requests"
"common.expired" = "Information expired"

# User authentication related error messages
"user.not.found" = "User not found"
"account.opt.code.expired.incorrect" = "Verification code expired or incorrect"
"account.access.token.expired.incorrect" = "Access token expired or incorrect"
"user.login.ban.time" = "User login is banned"

# 视频相关错误信息
"error.video.not.found" = "Video not found"
"error.video.not.published" = "Video not published"
"error.playlist.not.found" = "Playlist not found"
"error.video.collect.failed" = "Video collection failed"
"error.video.uncollect.failed" = "Video uncollection failed"
"error.video.share.failed" = "Video sharing failed"
