package errno

import "github.com/gogf/gf/v2/errors/gcode"

var (
	CodeFileClientConfigError      = gcode.New(50001, "file.client.config.error", nil)       // 文件配置错误
	CodeFileClientError            = gcode.New(50002, "file.client.error", nil)              // 文件客户端错误
	CodeFileClientHandleError      = gcode.New(50003, "file.client.handle.error", nil)       // 文件客户端处理错误
	CodeFileUploadError            = gcode.New(50004, "file.upload.error", nil)              // 文件上传错误
	CodeFileUploadSizeMax2MError   = gcode.New(50005, "file.upload.size.max.2M.error", nil)  // 文件上传大小超出限制
	CodeFileUploadMissingFileError = gcode.New(50006, "file.upload.missing.file.error", nil) // 文件上传缺少文件
	CodeFIleExtNotAllowedError     = gcode.New(50007, "file.ext.not.allowed.error", nil)     //文件上传后缀格式不允许
)
