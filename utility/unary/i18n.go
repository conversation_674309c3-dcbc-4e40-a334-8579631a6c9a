package unary

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"google.golang.org/grpc"
	"strings"
)

const AcceptLanguage = "accept-language"

// UnaryI18n 多语言中间件
func UnaryI18n(
	ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler,
) (interface{}, error) {
	requestLang := g.Cfg().MustGet(ctx, "i18n.default", "en-US").String()
	// 多语言设置
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	lang := headerMap.GetVar(AcceptLanguage).String()
	if len(lang) > 0 {
		lang = strings.Split(lang, ",")[0]
		if gi18n.T(gi18n.WithLanguage(context.TODO(), lang), AcceptLanguage) != AcceptLanguage {
			// 可以翻译 accept-language，设置语言
			requestLang = lang
		}
	}
	g.Log().Debug(ctx, "i18n", requestLang)
	// 设置请求上下文语言
	ctx = gi18n.WithLanguage(ctx, requestLang)
	return handler(ctx, req)
}
