package unary

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/util/gutil"
	"github.com/golang/protobuf/proto"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"halalplus/api/common"
)

// UnaryCommonError 错误处理中间件
func UnaryCommonError(
	ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler,
) (interface{}, error) {
	var err error
	var res interface{}
	gutil.TryCatch(ctx, func(ctx2 context.Context) {
		res, err = handler(ctx, req)
	}, func(ctx context.Context, exception error) {
		err = gerror.WrapCode(gcode.New(int(codes.Internal), "UnaryCommonError", nil), exception, "panic recovered")
	})

	if err != nil {
		code := gerror.Code(err)
		if code.Code() != -1 {
			ersp := &common.CommonResponse{
				Error: &common.Error{
					Reason: err.Error(),
				},
			}
			ersp.Code = int32(code.Code())
			ersp.Msg = gi18n.T(ctx, code.Message())

			// api错误返回包含堆栈信息
			if g.Cfg().MustGet(ctx, "grpc.errorStackInResponse", false).Bool() {
				ersp.Error.Detail = gerror.Stack(err)
			}

			// 错误处理完毕，返回数据
			err = nil
			res = ersp
		}
	}
	if res == nil {
		res = proto.Message(nil)
	}
	return res, err
}
