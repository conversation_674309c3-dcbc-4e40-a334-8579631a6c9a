package unary

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"google.golang.org/grpc"
)

// UnaryLogRequestResponse 请求返回值打印
func UnaryLogRequestResponse(
	ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler,
) (interface{}, error) {
	// 多语言设置
	headerMap := grpcx.Ctx.IncomingMap(ctx)

	g.Log().Debug(ctx, "REQUEST", headerMap, req)

	res, err := handler(ctx, req)

	g.Log().Debug(ctx, "RESPONSE", res, err)
	return res, err
}
